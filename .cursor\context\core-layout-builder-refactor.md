# CoreLayoutBuilderService Refactor - Stateless Utility Service

## M<PERSON><PERSON> tiêu
Refactor CoreLayoutBuilderService từ stateful service thành stateless utility service để loại bỏ tất cả state management và chuyển trách nhiệm quản lý state về cho các components.

## Những thay đổi đã thực hiện

### 1. Loại bỏ State Variables
✅ **HOÀN THÀNH**: Đã loại bỏ tất cả state variables:
- `private sectionsSubject = new BehaviorSubject<Section[]>([]);`
- `private loadingSubject = new BehaviorSubject<boolean>(false);`
- `private errorSubject = new BehaviorSubject<string | null>(null);`
- `public currentSections = signal<Section[]>([]);`

### 2. Transform Methods thành Pure Functions
✅ **HOÀN THÀNH**: Đã chuyển đổi tất cả methods thành pure functions:

#### Section Management
- `handleSectionTitleChange(sections: Section[], sectionId: string, newTitle: string): Section[]`
- `handleSectionDeletion(sections: Section[], sectionId: string): Section[]`
- `createDefaultSection(): Section` (không thay đổi - đã là pure function)

#### Field Management
- `addFieldToSection(sections: Section[], sectionId: string, fieldType: LayoutField): Section[]`
- `removeFieldFromSection(sections: Section[], sectionId: string, fieldId: string | number): Section[]`
- `updateFieldInSection(sections: Section[], sectionId: string, fieldId: string | number, updatedField: Partial<LayoutField>): Section[]`
- `handleFieldReorder(sections: Section[], sectionId: string, fields: LayoutField[]): Section[]`
- `handleFieldPropertiesUpdate(sections: Section[], sectionId: string, fieldId: string | number, updatedField: LayoutField): Section[]`
- `handleFieldDeletion(sections: Section[], sectionId: string, fieldId: number): Section[]`
- `handleQuickAddField(sections: Section[], sectionId: string, fieldType: string): Section[]`

#### Layout Operations
- `saveCurrentLayoutData(layout: DynamicLayoutConfig, sections: Section[]): DynamicLayoutConfig`
- `validateSectionsForSave(sections: Section[]): Section[]`
- `switchToLayoutBusinessLogic(layoutIndex: number, layouts: DynamicLayoutConfig[]): { isValid: boolean; newLayout: DynamicLayoutConfig | null }`
- `handleFieldReorderBusinessLogic(sections: Section[], sectionId: string, fields: LayoutField[]): Section[]`

#### Template Management
- `applyTemplateAndGetSections(template: Template): Section[]` (loại bỏ stateful version)

#### Storage Operations
- `saveLayout(sections: Section[], instanceId?: string): Observable<boolean>` (loại bỏ loading state management)
- `submitLayout(sections: Section[]): Observable<SubmitLayoutResponse>` (loại bỏ loading state management)

### 3. Loại bỏ Methods không cần thiết
✅ **HOÀN THÀNH**: Đã loại bỏ các methods không còn cần thiết:
- `updateSections()` - không còn cần vì không có internal state
- `clearError()` - không còn error state để clear
- `getCurrentSections()` - components tự quản lý sections
- `getCurrentLoadingState()` - components tự quản lý loading state
- `getCurrentError()` - components tự quản lý error state
- `cleanup()` - không còn BehaviorSubjects để cleanup

### 4. Cập nhật Components sử dụng Service
✅ **HOÀN THÀNH**: Đã cập nhật các components để sử dụng API mới:

#### CreateTabService
- `addFieldToSection()`: Sử dụng `coreService.addFieldToSection(currentSections, sectionId, fieldType)`
- `deleteFieldFromSection()`: Sử dụng `coreService.removeFieldFromSection(currentSections, sectionId, fieldId)`
- `reorderFieldsInSection()`: Sử dụng `coreService.handleFieldReorderBusinessLogic(currentSections, sectionId, fields)`
- `toggleFieldRequired()`: Sử dụng `coreService.updateFieldInSection(currentSections, sectionId, fieldId, update)`
- `updateFieldProperties()`: Sử dụng `coreService.updateFieldInSection(currentSections, sectionId, fieldId, field)`
- `updateFieldPermissions()`: Sử dụng `coreService.updateFieldInSection(currentSections, sectionId, fieldId, update)`
- `deleteSection()`: Sử dụng `coreService.handleSectionDeletion(currentSections, sectionId)`
- `updateSectionTitle()`: Sử dụng `coreService.handleSectionTitleChange(currentSections, sectionId, newTitle)`

#### QuickCreateTabService
- `removeFieldFromSection()`: Sử dụng `coreService.removeFieldFromSection(tempSections, sectionId, fieldId)`
- `reorderFieldsInSection()`: Sử dụng `coreService.handleFieldReorderBusinessLogic(tempSections, sectionId, fields)`

## Kiến trúc mới

### Trước khi refactor
```typescript
// Service quản lý state nội bộ
export class CoreLayoutBuilderService {
  private sectionsSubject = new BehaviorSubject<Section[]>([]);
  
  addFieldToSection(sectionId: string, fieldType: LayoutField): void {
    const currentSections = this.sectionsSubject.value;
    // ... logic
    this.sectionsSubject.next(updatedSections);
  }
}

// Component phụ thuộc vào service state
component.coreService.addFieldToSection(sectionId, fieldType);
const sections = component.coreService.getCurrentSections();
```

### Sau khi refactor
```typescript
// Service chỉ chứa pure functions
export class CoreLayoutBuilderService {
  addFieldToSection(sections: Section[], sectionId: string, fieldType: LayoutField): Section[] {
    // ... logic
    return updatedSections;
  }
}

// Component tự quản lý state
const currentSections = component.configStateService.getCreateTabSections();
const updatedSections = component.coreService.addFieldToSection(currentSections, sectionId, fieldType);
component.updateSections(updatedSections);
```

## Lợi ích của refactor

### 1. Tách biệt trách nhiệm rõ ràng
- **CoreLayoutBuilderService**: Chỉ chứa business logic, không quản lý state
- **Components**: Tự quản lý state của riêng mình
- **ConfigStateService**: Centralized state management

### 2. Tính dự đoán cao hơn
- Tất cả methods là pure functions
- Không có side effects
- Dễ test và debug

### 3. Tránh shared state conflicts
- Mỗi component có instance riêng của service
- Không có state sharing giữa các components
- Tránh race conditions

### 4. Dễ bảo trì
- Code đơn giản hơn, ít phức tạp
- Không cần quản lý lifecycle của BehaviorSubjects
- Dễ hiểu flow của data

## Kết quả
✅ **BUILD THÀNH CÔNG**: Tất cả lỗi TypeScript đã được sửa
✅ **FUNCTIONALITY PRESERVED**: Tất cả chức năng hiện tại được giữ nguyên
✅ **ARCHITECTURE IMPROVED**: Service giờ đây là stateless utility với pure functions
