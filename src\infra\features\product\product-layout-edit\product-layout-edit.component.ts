import { Component, OnIni<PERSON>, On<PERSON><PERSON>roy, ViewChild, signal, computed, inject, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

// Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Shared components
import { DynamicLayoutBuilderComponent } from '@shared/components/dynamic-layout-builder/dynamic-layout-builder.component';

// Models và Services - sử dụng interface mới
import { DynamicLayoutConfig, DynamicLayoutBuilderConfig } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-config.model';
import { simpleMockLayouts } from '@mock/product/simple_layout.mock';
import { FlashMessageService } from '@core/services/flash_message.service';
import { TranslateService } from '@ngx-translate/core';

/**
 * ProductLayoutEditComponent - Component chỉnh sửa layout sản phẩm
 *
 * Tính năng:
 * - Load template từ route parameter (index)
 * - Sử dụng DynamicLayoutBuilderComponent để chỉnh sửa
 * - Navigation back đến danh sách
 * - Error handling và loading states
 * - Sử dụng Angular 19 Signals và OnPush change detection
 */
@Component({
  selector: 'app-product-layout-edit',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatProgressSpinnerModule,
    DynamicLayoutBuilderComponent
  ],
  templateUrl: './product-layout-edit.component.html',
  styleUrls: ['./product-layout-edit.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductLayoutEditComponent implements OnDestroy {
  // Inject services - chỉ giữ những gì cần thiết
  private router = inject(Router);
  private flashMessageService = inject(FlashMessageService);
  private translateService = inject(TranslateService);
  private subscriptions = new Subscription();

  // Signals cho state management - đơn giản hóa chỉ giữ loading và error
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);

  // Layout builder config cho DynamicLayoutBuilderComponent - sử dụng mock data
  layoutBuilderConfig = signal<DynamicLayoutBuilderConfig>({
    layoutId: 'product-layout',
    selectedLayout: simpleMockLayouts[0],
    layouts: simpleMockLayouts,
    defaultLayoutConfig: simpleMockLayouts[0] // Simple layout làm default
  });

  // Computed signals
  hasLayoutConfigs = computed(() => this.layoutBuilderConfig().layouts.length > 0);

  // ViewChild reference to DynamicLayoutBuilderComponent
  @ViewChild('dynamicLayoutBuilder') dynamicLayoutBuilder!: DynamicLayoutBuilderComponent;

  // ngOnInit(): void {
    // Component chỉ đóng vai trò container, không cần load template từ route
    // Sử dụng mock data có sẵn trong layoutConfigs signal

    // console.log('🚀 ProductLayoutEditComponent initialized with mock data');
    // console.log('📋 Available layouts:', this.layoutBuilderConfig().layouts.map((config: DynamicLayoutConfig) => ({
    //   id: config._id,
    //   title: config.title,
    //   sectionsCount: config.sections?.length || 0
    // })));
  // }

  // ==================== EVENT HANDLERS FROM DYNAMIC LAYOUT BUILDER ====================

  /**
   * Xử lý khi layout thay đổi từ DynamicLayoutBuilderComponent
   */
  onLayoutChanged(layout: DynamicLayoutConfig): void {
    // console.log('📋 ProductLayoutEdit: Layout changed event received:', {
    //   id: layout._id,
    //   title: layout.title,
    //   sectionsCount: layout.sections?.length || 0
    // });

    // Chỉ log để verify communication, không cần xử lý phức tạp
    this.flashMessageService.info(
      this.translateService.instant('FLASH_MESSAGES.INFO.GENERAL.PROCESSING'),
      {
        description: `Switched to layout: ${layout.title}`
      }
    );
  }

  /**
   * Xử lý khi layout được lưu từ DynamicLayoutBuilderComponent
   */
  onLayoutSaved(layout: DynamicLayoutConfig): void {
    // console.log('💾 ProductLayoutEdit: Layout saved event received:', {
    //   id: layout._id,
    //   title: layout.title,
    //   sectionsCount: layout.sections?.length || 0
    // });

    // Chỉ log để verify communication
    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.SAVED'),
      {
        description: `Layout "${layout.title}" saved successfully`
      }
    );
  }

  /**
   * Xử lý khi layout được xóa từ DynamicLayoutBuilderComponent
   */
  onLayoutDeleted(layoutId: string): void {
    // console.log('🗑️ ProductLayoutEdit: Layout deleted event received:', layoutId);

    // Chỉ log để verify communication
    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.DELETED'),
      {
        description: `Layout with ID "${layoutId}" deleted`
      }
    );
  }

  /**
   * Xử lý khi layout mới được tạo từ DynamicLayoutBuilderComponent
   */
  onLayoutCreated(layout: DynamicLayoutConfig): void {
    // console.log('✨ ProductLayoutEdit: Layout created event received:', {
    //   id: layout._id,
    //   title: layout.title,
    //   sectionsCount: layout.sections?.length || 0
    // });

    // Chỉ log để verify communication
    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.CREATED'),
      {
        description: `New layout "${layout.title}" created successfully`
      }
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  // ==================== TEST METHODS ====================

  /**
   * Test method to trigger layout switching
   */
  testSwitchLayout(layoutId: string): void {
    // console.log('🧪 Test switching to layout:', layoutId);

    // Find layout config by ID
    const targetLayout = this.layoutBuilderConfig().layouts.find(layout => layout._id === layoutId);

    if (targetLayout) {
      // console.log('✅ Found target layout:', targetLayout.title);
      // Trigger onLayoutChanged directly to test the flow
      this.onLayoutChanged(targetLayout);
    } else {
      // console.error('❌ Layout not found with ID:', layoutId);
      this.flashMessageService.error(`Layout with ID "${layoutId}" not found`);
    }
  }


  // ==================== UTILITY METHODS ====================

  /**
   * Điều hướng về trang danh sách layout
   */
  onBackToList(): void {
    try {
      this.router.navigate(['/product/layouts']);
    } catch (err) {
      // console.error('Navigation error:', err);
    }
  }

}
