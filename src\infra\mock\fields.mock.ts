import { Field } from "@domain/entities/field.entity";

export const mockCustomerFields: Field[] = [
  {
    _id: '1',
    label: 'Customer Name',
    type: 'text',
    value: '',
    isPublic: false,
    isRequired: true,
    tooltip: 'Enter customer full name',
    constraints: { maxLength: 100, unique: false }
  },
  {
    _id: '2',
    label: 'Email Address',
    type: 'email',
    value: '',
    isPublic: true,
    isRequired: true,
    tooltip: 'Enter valid email address',
    constraints: { unique: true }
  },
  {
    _id: '3',
    label: 'Phone Number',
    type: 'phone',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter phone number',
    constraints: {}
  },
  {
    _id: '4',
    label: 'Age',
    type: 'number',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter age',
    constraints: { maxDigits: 3 }
  },
  {
    _id: '5',
    label: 'Salary',
    type: 'currency',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter salary amount',
    constraints: { maxDigits: 10, decimalPlaces: 2 }
  },
  {
    _id: '6',
    label: 'Birth Date',
    type: 'date',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Select birth date',
    constraints: {}
  },
  {
    _id: '7',
    label: 'Status',
    type: 'picklist',
    value: '',
    isPublic: true,
    isRequired: true,
    tooltip: 'Select status',
    constraints: {
      picklistValues: ['Active', 'Inactive', 'Pending', 'Suspended']
    }
  },
  {
    _id: '8',
    label: 'Skills',
    type: 'multi-picklist',
    value: [],
    isPublic: false,
    isRequired: false,
    tooltip: 'Select multiple skills',
    constraints: {
      picklistValues: ['JavaScript', 'TypeScript', 'Angular', 'React', 'Vue', 'Node.js', 'Python', 'Java']
    }
  },
  {
    _id: '9',
    label: 'Is Active',
    type: 'checkbox',
    value: false,
    isPublic: true,
    isRequired: false,
    tooltip: 'Check if active',
    constraints: {}
  },
  {
    _id: '10',
    label: 'Description',
    type: 'textarea',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter description',
    constraints: { textType: 'large', maxLength: 500 }
  },
  {
    _id: '11',
    label: 'Description 1',
    type: 'textarea',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter description',
    constraints: { textType: 'large', maxLength: 500 }
  },
  {
    _id: '12',
    label: 'Description 2',
    type: 'textarea',
    value: '',
    isPublic: false,
    isRequired: false,
    tooltip: 'Enter description',
    constraints: { textType: 'large', maxLength: 500 }
  }
];
