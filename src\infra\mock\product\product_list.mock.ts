import { ListConfig, ListConfigData, Product } from "@shared/models/view/list-layout.model";
import { <PERSON><PERSON><PERSON>cy<PERSON>ield, DateField, DecimalField, Field, MultiPicklistField, NumberField, PercentField, PicklistField, TextareaField, TextField } from "@domain/entities/field.entity";

const allColumns = [
  { _id: '_id', label: 'Mã SP', sortable: true, fieldType: 'text' },
  { _id: 'name', label: 'Tên sản phẩm', sortable: true, fieldType: 'text' },
  { _id: 'price', label: 'Giá', sortable: true, fieldType: 'currency' },
  { _id: 'stock', label: 'Tồn kho', sortable: false, fieldType: 'number' },
  { _id: 'category', label: 'Danh mục', sortable: false, fieldType: 'picklist' },
  { _id: 'brand', label: 'Thương hiệu', sortable: true, fieldType: 'text' },
  { _id: 'sku', label: 'SKU', sortable: true, fieldType: 'text' },
  { _id: 'weight', label: 'Trọng lượng (kg)', sortable: false, fieldType: 'decimal' },
  { _id: 'dimensions', label: 'Kích thước (cm)', sortable: false, fieldType: 'text' },
  { _id: 'color', label: 'Màu sắc', sortable: false, fieldType: 'multi-picklist' },
  { _id: 'material', label: 'Chất liệu', sortable: false, fieldType: 'text' },
  { _id: 'origin', label: 'Xuất xứ', sortable: false, fieldType: 'picklist' },
  { _id: 'warranty', label: 'Bảo hành (tháng)', sortable: true, fieldType: 'number' },
  { _id: 'releaseDate', label: 'Ngày phát hành', sortable: true, fieldType: 'date' },
  { _id: 'discount', label: 'Giảm giá (%)', sortable: true, fieldType: 'percent' },
  { _id: 'rating', label: 'Đánh giá', sortable: true, fieldType: 'decimal' },
  { _id: 'sales', label: 'Doanh số', sortable: true, fieldType: 'number' },
  { _id: 'status', label: 'Trạng thái', sortable: false, fieldType: 'picklist' },
  { _id: 'supplier', label: 'Nhà cung cấp', sortable: false, fieldType: 'text' },
  { _id: 'description', label: 'Mô tả', sortable: false, fieldType: 'textarea' }
];



const mockFields: Field[] = [
  {
    _id: 'id',
    label: 'Mã SP',
    type: 'text',
    value: '',
    isRequired: true,
    constraints: {
      maxLength: 50,
      unique: true,
    },
  } as TextField,
  {
    _id: 'name',
    label: 'Tên sản phẩm',
    type: 'text',
    value: '',
    isRequired: true,
    constraints: {
      maxLength: 100,
    },
  } as TextField,
  {
    _id: 'price',
    label: 'Giá',
    type: 'currency',
    value: '0',
    isRequired: true,
    constraints: {
      maxDigits: 10,
      decimalPlaces: 2,
      rounding: 'normal',
    },
  } as CurrencyField,
  {
    _id: 'stock',
    label: 'Tồn kho',
    type: 'number',
    value: '0',
    constraints: {
      maxDigits: 10,
    },
  } as NumberField,
  {
    _id: 'category',
    label: 'Danh mục',
    type: 'picklist',
    value: '',
    constraints: {
      picklistValues: ['Điện tử', 'Thời trang', 'Đồ gia dụng', 'Thực phẩm'],
      sortOrder: 'input',
      defaultValue: '',
    },
  } as PicklistField,
  {
    _id: 'brand',
    label: 'Thương hiệu',
    type: 'text',
    value: '',
    constraints: {
      maxLength: 50,
    },
  } as TextField,
  {
    _id: 'sku',
    label: 'SKU',
    type: 'text',
    value: '',
    constraints: {
      maxLength: 50,
      unique: true,
    },
  } as TextField,
  {
    _id: 'weight',
    fieldId: 'decimal',
    label: 'Trọng lượng (kg)',
    type: 'decimal',
    value: '0',
    constraints: {
      maxDigits: 10,
      decimalPlaces: 2,
      useNumberSeparator: true,
    },
  } as DecimalField,
  {
    _id: 'dimensions',
    fieldId: 'text',
    label: 'Kích thước (cm)',
    type: 'text',
    value: '',
    constraints: {
      maxLength: 50,
    },
  } as TextField,
  {
    _id: 'color',
    fieldId: 'multi-picklist',
    label: 'Màu sắc',
    type: 'multi-picklist',
    value: [],
    constraints: {
      picklistValues: ['Đỏ', 'Xanh', 'Vàng', 'Đen', 'Trắng'],
      sortOrder: 'alphabetical',
      defaultValue: [],
    },
  } as MultiPicklistField,
  {
    _id: 'material',
    fieldId: 'text',
    label: 'Chất liệu',
    type: 'text',
    value: '',
    constraints: {
      maxLength: 100,
    },
  } as TextField,
  {
    _id: 'origin',
    fieldId: 'picklist',
    label: 'Xuất xứ',
    type: 'picklist',
    value: '',
    constraints: {
      picklistValues: ['Việt Nam', 'Trung Quốc', 'Mỹ', 'Nhật Bản', 'Hàn Quốc'],
      sortOrder: 'input',
      defaultValue: '',
    },
  } as PicklistField,
  {
    _id: 'warranty',
    fieldId: 'number',
    label: 'Bảo hành (tháng)',
    type: 'number',
    value: '0',
    constraints: {
      maxDigits: 3,
    },
  } as NumberField,
  {
    _id: 'releaseDate',
    fieldId: 'date',
    label: 'Ngày phát hành',
    type: 'date',
    value: '',
    constraints: {},
  } as DateField,
  {
    _id: 'discount',
    fieldId: 'percent',
    label: 'Giảm giá (%)',
    type: 'percent',
    value: '0',
    constraints: {},
  } as PercentField,
  {
    _id: 'rating',
    fieldId: 'decimal',
    label: 'Đánh giá',
    type: 'decimal',
    value: '0',
    constraints: {
      maxDigits: 3,
      decimalPlaces: 1,
      useNumberSeparator: true,
    },
  } as DecimalField,
  {
    _id: 'sales',
    fieldId: 'number',
    label: 'Doanh số',
    type: 'number',
    value: '0',
    constraints: {
      maxDigits: 10,
    },
  } as NumberField,
  {
    _id: 'status',
    fieldId: 'picklist',
    label: 'Trạng thái',
    type: 'picklist',
    value: '',
    constraints: {
      picklistValues: ['Còn hàng', 'Hết hàng', 'Ngừng kinh doanh'],
      sortOrder: 'input',
      defaultValue: 'Còn hàng',
    },
  } as PicklistField,
  {
    _id: 'supplier',
    fieldId: 'text',
    label: 'Nhà cung cấp',
    type: 'text',
    value: '',
    constraints: {
      maxLength: 100,
    },
  } as TextField,
  {
    _id: 'description',
    fieldId: 'textarea',
    label: 'Mô tả',
    type: 'textarea',
    value: '',
    constraints: {
      textType: 'rich',
      maxLength: 1000,
    },
  } as TextareaField,
];

const mockProducts: Product[] = Array.from({ length: 500 }, (_, index) => ({
  _id: `${index + 1}`,
  name: index !== 0 ? `Sản phẩm ${index + 1}` : 'Sản phẩm 0 Sản phẩm 0 Sản phẩm 0 Sản phẩm 0 Sản phẩm 0 Sản phẩm 0 Sản phẩm 0 Sản phẩm 0 Sản phẩm 0 Sản phẩm 0 ',
  // name: `Sản phẩm ${index + 1}`,
  price: Math.round(Math.random() * 500 + 50),
  stock: Math.round(Math.random() * 100),
  category: ['fashion', 'fashion2', 'electronics', 'beauty', 'home'][Math.floor(Math.random() * 4)],
  brand: ['Nike', 'Samsung', "L'Oréal", 'IKEA'][Math.floor(Math.random() * 4)],
  sku: `SKU-${index + 1}-${Math.random().toString(36).slice(2, 7)}`,
  weight: Number((Math.random() * 5 + 0.1).toFixed(2)),
  dimensions: `${Math.round(Math.random() * 50)}x${Math.round(Math.random() * 50)}x${Math.round(Math.random() * 50)}`,
  color: ['Đỏ', 'Xanh', 'Đen', 'Trắng'][Math.floor(Math.random() * 4)],
  material: ['Vải', 'Nhựa', 'Kim loại', 'Gỗ'][Math.floor(Math.random() * 4)],
  origin: ['Việt Nam', 'Trung Quốc', 'Mỹ', 'Nhật Bản'][Math.floor(Math.random() * 4)],
  warranty: Math.round(Math.random() * 24),
  releaseDate: `202${Math.floor(Math.random() * 5)}-0${Math.floor(Math.random() * 9) + 1}-01`,
  discount: Math.round(Math.random() * 50),
  rating: Number((Math.random() * 5).toFixed(1)),
  sales: Math.round(Math.random() * 1000),
  status: ['Còn hàng', 'Hết hàng', 'Sắp có'][Math.floor(Math.random() * 3)],
  supplier: ['Công ty A', 'Công ty B', 'Công ty C'][Math.floor(Math.random() * 3)],
  description: `Mô tả sản phẩm ${index + 1}`
}));

export const mockProductListFetchNewItems: ListConfig['fetchNewItems'] = async (data) => {
  const page = data?.page ?? 1;
  const pageSize = data?.pageSize ?? 20;

  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const items = mockProducts.slice(start, end);

  return {
    items,
    pager: {
      currentPage: page,
      totalPages: Math.ceil(mockProducts.length / pageSize),
      pageSize
    }
  }
}

export const mockProductListPage: ListConfigData = {
  columnConfig: {
    allColumns,
    regularColumns: [
      { _id: 'stock', label: 'Tồn kho', sortable: false, fieldType: 'number' },
      { _id: 'category', label: 'Danh mục', sortable: false, fieldType: 'picklist' },
      { _id: 'brand', label: 'Thương hiệu', sortable: true, fieldType: 'text' },
      { _id: 'sku', label: 'SKU', sortable: true, fieldType: 'text' },
      { _id: 'weight', label: 'Trọng lượng (kg)', sortable: false, fieldType: 'decimal' },
      { _id: 'color', label: 'Màu sắc', sortable: false, fieldType: 'multi-picklist' },
      { _id: 'warranty', label: 'Bảo hành (tháng)', sortable: true, fieldType: 'number' },
      { _id: 'rating', label: 'Đánh giá', sortable: true, fieldType: 'decimal' }
    ],
    pinnedColumns: [
      { _id: '_id', label: 'Mã SP', sortable: true, fieldType: 'text' },
      { _id: 'name', label: 'Tên sản phẩm', sortable: true, fieldType: 'text' },
      { _id: 'price', label: 'Giá', sortable: true, fieldType: 'currency' }
    ],
    maxPinnedColumns: 3,
    columnOrder: [
      'stock',
      'category',
      'brand',
      'sku',
      'weight',
      'color',
      'warranty',
      'rating'
    ],
  },

  items: mockProducts.slice(0, 50),
  pager: {
    currentPage: 1,
    totalPages: 10, // Sẽ tính lại trong fetchProducts
    pageSize: 50
  },

  fieldFiltersConfig: {
    fields: mockFields,
    showClearAll: true,
    savedFilters: [
      {
        id: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        name: 'Expensive Products',
        filters: [
          {
            fieldId: 'price',
            filterValue: {
              operator: 'greater_than',
              value: 100
            }
          }
        ]
      },
      {
        id: 'f47ac10b-58cc-4372-a567-0e02b2c3d480',
        name: 'Electronics Category',
        filters: [
          {
            fieldId: 'category',
            filterValue: {
              operator: 'is',
              values: ['Điện tử']
            }
          }
        ]
      },
      {
        id: 'f47ac10b-58cc-4372-a567-0e02b2c3d481',
        name: 'High Rating Products',
        filters: [
          {
            fieldId: 'rating',
            filterValue: {
              operator: 'greater_than_or_equal',
              value: 4.0
            }
          },
          {
            fieldId: 'stock',
            filterValue: {
              operator: 'greater_than',
              value: 10
            }
          }
        ]
      }
    ]
  },
}
