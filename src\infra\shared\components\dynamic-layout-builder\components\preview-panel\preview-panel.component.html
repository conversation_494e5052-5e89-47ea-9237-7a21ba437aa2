<div class="preview-panel-container">

  <!-- Header -->
  <div class="preview-header">
    <h4>{{ 'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.TITLE' | translate }}</h4>
    <p class="preview-description">
      {{ 'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.DESCRIPTION' | translate }}
    </p>

    <!-- Preview Controls -->
    <div class="preview-controls">
      <button mat-button
              (click)="onFillSampleData()"
              [disabled]="!hasAnyFields()">
        <mat-icon>auto_fix_high</mat-icon>
        {{ 'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.FILL_SAMPLE' | translate }}
      </button>

      <button mat-button
              (click)="onClearData()"
              [disabled]="!hasAnyData()">
        <mat-icon>clear_all</mat-icon>
        {{ 'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.CLEAR_DATA' | translate }}
      </button>

      <button mat-raised-button
              color="primary"
              (click)="onValidateForm()"
              [disabled]="!hasAnyFields()">
        <mat-icon>check_circle</mat-icon>
        {{ 'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.VALIDATE' | translate }}
      </button>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!hasAnyFields()" class="empty-state">
    <mat-icon class="empty-icon">preview</mat-icon>
    <h5>{{ 'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.NO_FIELDS' | translate }}</h5>
    <p>{{ 'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.NO_FIELDS_DESCRIPTION' | translate }}</p>
  </div>

  <!-- Preview Form -->
  <div *ngIf="hasAnyFields()" class="preview-form">

    <!-- Tabs for different views -->
    <mat-tab-group class="preview-tabs">

      <!-- Form View Tab -->
      <mat-tab [label]="'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.FORM_VIEW' | translate">
        <div class="form-view-content">

          <!-- Sections -->
          <mat-card *ngFor="let section of sections(); trackBy: trackBySection"
                    class="section-card">

            <mat-card-header>
              <mat-card-title class="section-title">
                <mat-icon>view_module</mat-icon>
                {{ section.title }}
              </mat-card-title>
              <mat-card-subtitle>
                {{ section.fields.length }}
                {{ 'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.FIELDS' | translate }}
              </mat-card-subtitle>
            </mat-card-header>

            <mat-card-content>
              <div class="fields-grid">

                <!-- Fields -->
                <div *ngFor="let field of section.fields; trackBy: trackByField"
                     class="field-container"
                     [class]="'field-type-' + field.type">

                  <!-- Text Field -->
                  <mat-form-field *ngIf="field.type === 'text'"
                                  appearance="outline"
                                  class="field-input">
                    <mat-label>
                      {{ field.label }}
                      <span *ngIf="field.required" class="required-asterisk">*</span>
                    </mat-label>
                    <input matInput
                           [(ngModel)]="previewData()[getFieldKey(field)]"
                           [required]="field.required || field.isRequired || false"
                           [placeholder]="field.placeholder || ''">
                  </mat-form-field>

                  <!-- Number Field -->
                  <mat-form-field *ngIf="field.type === 'number'"
                                  appearance="outline"
                                  class="field-input">
                    <mat-label>
                      {{ field.label }}
                      <span *ngIf="field.required" class="required-asterisk">*</span>
                    </mat-label>
                    <input matInput
                           type="number"
                           [(ngModel)]="previewData()[getFieldKey(field)]"
                           [required]="field.required || field.isRequired || false"
                           [placeholder]="field.placeholder || ''">
                  </mat-form-field>

                  <!-- Email Field -->
                  <mat-form-field *ngIf="field.type === 'email'"
                                  appearance="outline"
                                  class="field-input">
                    <mat-label>
                      {{ field.label }}
                      <span *ngIf="field.required" class="required-asterisk">*</span>
                    </mat-label>
                    <input matInput
                           type="email"
                           [(ngModel)]="previewData()[getFieldKey(field)]"
                           [required]="field.required || field.isRequired || false"
                           [placeholder]="field.placeholder || ''">
                  </mat-form-field>

                  <!-- Phone Field -->
                  <mat-form-field *ngIf="field.type === 'phone'"
                                  appearance="outline"
                                  class="field-input">
                    <mat-label>
                      {{ field.label }}
                      <span *ngIf="field.required" class="required-asterisk">*</span>
                    </mat-label>
                    <input matInput
                           type="tel"
                           [(ngModel)]="previewData()[getFieldKey(field)]"
                           [required]="field.required || field.isRequired || false"
                           [placeholder]="field.placeholder || ''">
                  </mat-form-field>

                  <!-- Textarea Field -->
                  <mat-form-field *ngIf="field.type === 'textarea'"
                                  appearance="outline"
                                  class="field-input field-textarea">
                    <mat-label>
                      {{ field.label }}
                      <span *ngIf="field.required" class="required-asterisk">*</span>
                    </mat-label>
                    <textarea matInput
                              [(ngModel)]="previewData()[getFieldKey(field)]"
                              [required]="field.required || field.isRequired || false"
                              [placeholder]="field.placeholder || ''"
                              rows="3">
                    </textarea>
                  </mat-form-field>

                  <!-- Date Field -->
                  <mat-form-field *ngIf="field.type === 'date'"
                                  appearance="outline"
                                  class="field-input">
                    <mat-label>
                      {{ field.label }}
                      <span *ngIf="field.required" class="required-asterisk">*</span>
                    </mat-label>
                    <input matInput
                           [matDatepicker]="picker"
                           [(ngModel)]="previewData()[getFieldKey(field)]"
                           [required]="field.required || field.isRequired || false">
                    <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                  </mat-form-field>

                  <!-- Select Field -->
                  <mat-form-field *ngIf="field.type === 'picklist'"
                                  appearance="outline"
                                  class="field-input">
                    <mat-label>
                      {{ field.label }}
                      <span *ngIf="field.required" class="required-asterisk">*</span>
                    </mat-label>
                    <mat-select [(ngModel)]="previewData()[getFieldKey(field)]"
                                [required]="field.required || false">
                      <mat-option *ngFor="let option of getSelectOptions(field)"
                                  [value]="option.value">
                        {{ option.label }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>

                  <!-- Checkbox Field -->
                  <div *ngIf="field.type === 'checkbox'" class="checkbox-field">
                    <mat-checkbox [(ngModel)]="previewData()[getFieldKey(field)]"
                                  [required]="field.required || field.isRequired || false">
                      {{ field.label }}
                      <span *ngIf="field.required" class="required-asterisk">*</span>
                    </mat-checkbox>
                  </div>

                  <!-- Radio Field -->
                  <div *ngIf="field.type === 'multi-picklist'" class="radio-field">
                    <label class="field-label">
                      {{ field.label }}
                      <span *ngIf="field.required" class="required-asterisk">*</span>
                    </label>
                    <mat-radio-group [(ngModel)]="previewData()[getFieldKey(field)]"
                                     [required]="field.required || false">
                      <mat-radio-button *ngFor="let option of getRadioOptions(field)"
                                        [value]="option.value"
                                        class="radio-option">
                        {{ option.label }}
                      </mat-radio-button>
                    </mat-radio-group>
                  </div>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- Data View Tab -->
      <mat-tab [label]="'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.DATA_VIEW' | translate">
        <div class="data-view-content">
          <mat-card class="data-card">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>data_object</mat-icon>
                {{ 'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.FORM_DATA' | translate }}
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <pre class="data-json">{{ getFormDataJson() }}</pre>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>

  <!-- Validation Results -->
  <div *ngIf="validationResults().length > 0" class="validation-results">
    <mat-card class="validation-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon [class]="hasValidationErrors() ? 'error-icon' : 'success-icon'">
            {{ hasValidationErrors() ? 'error' : 'check_circle' }}
          </mat-icon>
          {{ 'DYNAMIC_LAYOUT_BUILDER.PREVIEW_PANEL.VALIDATION_RESULTS' | translate }}
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngFor="let result of validationResults()"
             class="validation-item"
             [class]="result.type">
          <mat-icon class="validation-icon">
            {{ result.type === 'error' ? 'error' : 'warning' }}
          </mat-icon>
          <span class="validation-message">{{ result.message }}</span>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
