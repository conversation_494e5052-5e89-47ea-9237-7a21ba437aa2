import { Component, Input, Output, EventEmitter, signal, Signal, OnDestroy, inject, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CdkDragDrop, CdkDrag, CdkDropList, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import {DragData } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-builder.model';
import { FieldItemComponent } from './field-item/field-item.component';
import { Field } from '@domain/entities/field.entity';
import { FlashMessageService } from '@core/services/flash_message.service';
import { DynamicLayoutConfigStateService } from '@/shared/components/dynamic-layout-builder/services/dynamic-layout-config-state.service';
import { FieldPermissionProfile } from '@domain/entities/field.entity';
import { FieldPropertiesData } from '@/shared/components/dynamic-layout-builder/modals/field-properties/field-properties-modal.component';

/**
 * Component hiển thị danh sách fields trong một section
 * Hỗ trợ drag & drop để sắp xếp lại thứ tự fields
 */
@Component({
  selector: 'app-field-list',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatDividerModule,
    TranslateModule,
    CdkDropList,
    CdkDrag,
    FieldItemComponent
  ],
  templateUrl: './field-list.component.html',
  styleUrls: ['./field-list.component.scss']
})
export class FieldListComponent implements OnDestroy {

  /**
   * Injected services
   */
  private flashMessageService = inject(FlashMessageService);
  private translateService = inject(TranslateService);
  private configStateService = inject(DynamicLayoutConfigStateService);

  /**
   * Computed properties để lấy dữ liệu từ currentLayout
   */
  private currentLayout = this.configStateService.currentLayout;
  public defaultProfiles = computed(() => this.currentLayout()?.defaultProfiles || []);
  public availableSearchModules = computed(() => this.currentLayout()?.fieldSettings?.availableSearchModules || []);

  /**
   * Danh sách fields để hiển thị
   */
  @Input() fields!: Signal<Field[]>;

  /**
   * ID của section chứa field list này
   */
  @Input() sectionId!: Signal<string>;

  /**
   * Event khi thứ tự fields thay đổi
   */
  @Output() fieldsReordered = new EventEmitter<Field[]>();

  /**
   * Event khi toggle required status
   */
  @Output() fieldRequiredToggled = new EventEmitter<{ field: Field; isRequired: boolean }>();

  /**
   * Event khi edit properties
   */
  @Output() fieldPropertiesEdit = new EventEmitter<Field>();

  /**
   * Event khi set permission
   */
  @Output() fieldPermissionSet = new EventEmitter<Field>();

  /**
   * Event khi delete field
   */
  @Output() fieldDeleted = new EventEmitter<Field>();

  /**
   * Event khi quick add field được click
   */
  @Output() quickAddField = new EventEmitter<string>();

  /**
   * Event khi field label thay đổi
   */
  @Output() fieldLabelChanged = new EventEmitter<{ field: Field; newLabel: string }>();

  // Signals cho drag & drop state management
  showInsertionIndicator = signal<boolean>(false);
  insertionIndex = signal<number>(-1);
  insertionPosition = signal<'before' | 'after'>('before');
  isDragOver = signal<boolean>(false);
  draggedField = signal<Field | null>(null);

  // Track external drag operations (from sidebar)
  isExternalDrag = false;
  isFieldDragging = false;
  private timeoutUnsetExternalDrag!: ReturnType<typeof setTimeout>;

  /**
   * Cleanup on destroy
   */
  ngOnDestroy(): void {
    // Cleanup any remaining timeouts
    if (this.timeoutUnsetExternalDrag) {
      clearTimeout(this.timeoutUnsetExternalDrag);
    }
  }

  /**
   * Handle Angular CDK drag & drop reordering
   */
  onFieldDrop(event: CdkDragDrop<Field[]>): void {
    if (event.previousIndex !== event.currentIndex) {
      const fields = [...this.fields()];
      moveItemInArray(fields, event.previousIndex, event.currentIndex);

      // Update order for all fields
      fields.forEach((field, index) => {
        field.order = index + 1;
      });

      this.fieldsReordered.emit(fields);
      // console.log('✅ Reordered field via Angular CDK:', fields[event.currentIndex].label);
    }
  }

  /**
   * Tạo drag data cho field với thông tin section
   */
  createFieldDragData(field: Field): DragData {
    return {
      type: 'field',
      field: field,
      sourceSection: this.sectionId()
    };
  }











  /**
   * Xử lý khi field label thay đổi
   * ✅ FIXED: Emit fieldLabelChanged event để parent component handle update
   */
  onFieldLabelChanged(event: { field: Field; newLabel: string }): void {
    // ✅ FIXED: Emit fieldLabelChanged event để parent component cập nhật data
    // Parent component sẽ update data và trigger change detection
    this.fieldLabelChanged.emit(event);
  }

  /**
   * Xử lý khi toggle required status của field
   */
  onFieldRequiredToggled(event: { field: Field; isRequired: boolean }): void {
    // Cập nhật field trong danh sách
    const fields = [...this.fields()];
    const fieldIndex = fields.findIndex(f => f._id === event.field._id);
    if (fieldIndex !== -1) {
      fields[fieldIndex] = { ...fields[fieldIndex], isRequired: event.isRequired };
      this.fieldsReordered.emit(fields); // Sử dụng event có sẵn để cập nhật
    }

    // Emit event cho parent component
    this.fieldRequiredToggled.emit(event);
  }

  /**
   * Toggle required status của field (legacy method)
   */
  onToggleRequired(field: Field): void {
    this.fieldRequiredToggled.emit({
      field,
      isRequired: !field.isRequired
    });
  }

  /**
   * Xử lý khi edit properties của field
   */
  onEditProperties(updatedField: Field): void {
    // Cập nhật field trong danh sách local
    const fields = [...this.fields()];
    const fieldIndex = fields.findIndex(f => f._id === updatedField._id || f._id === updatedField._id);
    if (fieldIndex !== -1) {
      fields[fieldIndex] = { ...updatedField };
      this.fieldsReordered.emit(fields); // Sử dụng event có sẵn để cập nhật
      // console.log('✅ Field properties updated in field-list:', updatedField.label);
    }

    // Emit event cho parent component
    this.fieldPropertiesEdit.emit(updatedField);
  }

  /**
   * Xử lý khi set permission của field
   */
  onSetPermission(updatedField: Field): void {
    // Cập nhật field trong danh sách local
    const fields = [...this.fields()];
    const fieldIndex = fields.findIndex(f => f._id === updatedField._id || f._id === updatedField._id);
    if (fieldIndex !== -1) {
      fields[fieldIndex] = { ...updatedField };
      this.fieldsReordered.emit(fields); // Sử dụng event có sẵn để cập nhật
      console.log('✅ Field permissions updated in field-list:', updatedField._id);
    }

    // Emit event cho parent component
    this.fieldPermissionSet.emit(updatedField);
  }

  /**
   * Xóa field
   */
  onDeleteField(field: Field): void {
    this.fieldDeleted.emit(field);
  }



  /**
   * Xử lý khi quick add field được click
   */
  onQuickAddField(fieldType: string): void {
    this.quickAddField.emit(fieldType);
  }

  /**
   * External Drag Over Event Handler (from sidebar)
   */
  onExternalDragOver(event: DragEvent): void {
    event.preventDefault(); // Allow drop
    event.stopPropagation();

    if(this.isFieldDragging) {
      return;
    }

    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'copy';
    }

    if(this.timeoutUnsetExternalDrag) {
      clearTimeout(this.timeoutUnsetExternalDrag);
    }

    // console.log('🔄 External drag over field list', this.draggedField());

    this.isExternalDrag = true;
    // if(!this.isDragOver()) {
    //   this.isDragOver.set(true);
    // }
  }

  /**
   * External Drag Enter Event Handler (from sidebar)
   */
  onExternalDragEnter(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    if(this.isFieldDragging) {
      return;
    }

    if(this.timeoutUnsetExternalDrag) {
      clearTimeout(this.timeoutUnsetExternalDrag);
    }

    // console.log('🎯 External drag entered field list:', this.sectionId(), this.isExternalDrag, this.draggedField());

    this.isExternalDrag = true;
    if(!this.isDragOver()) {
      this.isDragOver.set(true);
    }
  }

  /**
   * External Drag Leave Event Handler (from sidebar)
   */
  onExternalDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    // console.log('🚪 External drag left field list', event);

    // Reset visual feedback with delay to prevent flicker
    this.timeoutUnsetExternalDrag = setTimeout(() => {
      this.isDragOver.set(false);
      this.isExternalDrag = false;
    }, 50);
  }



  /**
   * External Drop Event Handler (from sidebar)
   */
  onExternalDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    // console.log('🎯 External drop event in field list:', this.sectionId());

    // Reset visual feedback
    this.isDragOver.set(false);
    this.isExternalDrag = false;

    // Get drag data
    if (event.dataTransfer) {
      try {
        // console.log(event);
        const dragDataStr = event.dataTransfer.getData('application/json');
        if(!dragDataStr) {
          return;
        }
        const dragData = JSON.parse(dragDataStr);

        // console.log('📦 External drop data:', dragData);

        if (dragData.type === 'field-type-clone' && dragData.fieldType) {
          // Dịch label từ translation key sang text tiếng Việt
          const translatedLabel = this.getFieldTypeLabel(dragData.fieldType.type);

          // Create new field from field type
          const newField: Field = {
            _id: `temp-${Date.now()}_${Math.floor(Math.random() * 1000)}`, // Temporary ID với prefix 'temp-'
            label: translatedLabel, // Sử dụng label đã dịch thay vì translation key
            type: dragData.fieldType.type,
            isRequired: false,
            isPublic: true,
            order: this.fields().length + 1,
            constraints: dragData.fieldType.constraints || {}
          };

          // Add field to list
          const fields = [...this.fields(), newField];

          // Update order for all fields
          fields.forEach((field, index) => {
            field.order = index + 1;
          });

          // Note: fields is a readonly Signal, parent component will update it via fieldsReordered event

          // Emit event
          this.fieldsReordered.emit(fields);

          // Show success toast với label đã dịch
          this.flashMessageService.success(
            this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.MESSAGES.FIELD_ADDED', { field: translatedLabel })
          );

          // console.log('✅ Added new field from external drop:', translatedLabel);
        }
      } catch (error) {
        // console.error('❌ Error parsing external drop data:', error);
        // Show error toast
        this.flashMessageService.error(
          this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.MESSAGES.FIELD_ADD_ERROR')
        );
      }
    }
  }





  /**
   * TrackBy function cho ngFor
   */
  trackByField(_index: number, field: Field): string | number {
    return field._id || _index;
  }

  /**
   * Lấy label hiển thị cho field type
   */
  getFieldTypeLabel(type: string): string {
    const typeMap: { [key: string]: string } = {
      'text': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.TEXT'),
      'integer': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.NUMBER'),
      'decimal': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.NUMBER'),
      'percent': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.NUMBER'),
      'currency': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.NUMBER'),
      'date': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.DATE'),
      'datetime': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.DATE'),
      'email': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.EMAIL'),
      'phone': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.PHONE'),
      'picklist': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.SELECT'),
      'multi-picklist': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.SELECT'),
      'url': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.TEXT'),
      'textarea': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.TEXT_AREA'),
      'checkbox': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.CHECKBOX'),
      'select': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.SELECT'),
      'number': this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.NUMBER')
    };

    return typeMap[type] || type;
  }
}
