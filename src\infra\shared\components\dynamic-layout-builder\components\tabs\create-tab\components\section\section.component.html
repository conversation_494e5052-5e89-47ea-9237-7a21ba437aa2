<div class="section-container" cdkDrag [cdkDragData]="sectionSignal()">
  <!-- Section header -->
  <div class="section-header">
    <!-- Drag handle -->
    <div class="drag-handle" cdkDragHandle>
      <mat-icon>drag_indicator</mat-icon>
    </div>

    <!-- Section title (inline editable) -->
    <div class="section-title" *ngIf="!isEditingTitle()">
      <h4 class="title-text" (click)="startEditTitle()">
        {{ sectionSignal().title }}
      </h4>
      <mat-icon class="edit-icon" (click)="startEditTitle()">edit</mat-icon>
    </div>

    <!-- Title editor -->
    <div class="title-editor" *ngIf="isEditingTitle()">
      <mat-form-field appearance="outline" class="title-input">
        <input
          matInput
          [(ngModel)]="editingTitle"
          (keydown.enter)="saveTitle()"
          (keydown.escape)="cancelEditTitle()"
          (blur)="saveTitle()"
          #titleInput
          [placeholder]="'DYNAMIC_LAYOUT_BUILDER.SECTION.TITLE_PLACEHOLDER' | translate">
      </mat-form-field>
    </div>

    <!-- Section actions -->
    <div class="section-actions">
      <!-- Collapse/Expand button -->
      <button
        mat-icon-button
        (click)="toggleCollapse()"
        class="collapse-btn"
        [attr.aria-label]="(isCollapsed() ? 'DYNAMIC_LAYOUT_BUILDER.SECTION.EXPAND' : 'DYNAMIC_LAYOUT_BUILDER.SECTION.COLLAPSE') | translate"
        >
        <mat-icon>{{ isCollapsed() ? 'expand_more' : 'expand_less' }}</mat-icon>
      </button>

      <!-- Quick add field menu -->
      <button
        mat-icon-button
        [matMenuTriggerFor]="quickAddMenu"
        class="add-field-btn"
        [attr.aria-label]="'DYNAMIC_LAYOUT_BUILDER.SECTION.ADD_FIELD' | translate"
        matTooltip="{{ 'DYNAMIC_LAYOUT_BUILDER.SECTION.ADD_FIELD' | translate }}"
        >
        <mat-icon>add</mat-icon>
      </button>

      <!-- Quick add field menu -->
      <mat-menu #quickAddMenu="matMenu" class="quick-add-field-menu">
        <button mat-menu-item (click)="onQuickAddField('text')">
          <mat-icon>text_fields</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.TEXT' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('number')">
          <mat-icon>numbers</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.NUMBER' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('email')">
          <mat-icon>email</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.EMAIL' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('phone')">
          <mat-icon>phone</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.PHONE' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('textarea')">
          <mat-icon>notes</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.TEXTAREA' | translate }}</span>
        </button>



        <button mat-menu-item (click)="onQuickAddField('date')">
          <mat-icon>calendar_today</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.DATE' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('datetime')">
          <mat-icon>schedule</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.DATETIME' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('file')">
          <mat-icon>attach_file</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.FILE' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('image')">
          <mat-icon>image</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.IMAGE' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('checkbox')">
          <mat-icon>check_box</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.CHECKBOX' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('radio')">
          <mat-icon>radio_button_checked</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.RADIO' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('select')">
          <mat-icon>arrow_drop_down</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.SELECT' | translate }}</span>
        </button>


        <button mat-menu-item (click)="onQuickAddField('size')">
          <mat-icon>straighten</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.SIZE' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('color')">
          <mat-icon>palette</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.COLOR' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('brand')">
          <mat-icon>business</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.BRAND' | translate }}</span>
        </button>

        <button mat-menu-item (click)="onQuickAddField('category')">
          <mat-icon>category</mat-icon>
          <span>{{ 'DYNAMIC_LAYOUT_BUILDER.CATEGORY' | translate }}</span>
        </button>
      </mat-menu>

      <!-- Delete section -->
      <button
        mat-icon-button
        (click)="onDeleteSection()"
        class="delete-btn"
        [attr.aria-label]="'DYNAMIC_LAYOUT_BUILDER.SECTION.DELETE' | translate"
        matTooltip="{{ 'DYNAMIC_LAYOUT_BUILDER.SECTION.DELETE' | translate }}"
        >
        <mat-icon>delete</mat-icon>
      </button>
    </div>
  </div>

  <!-- Section content (ẩn/hiện dựa trên isCollapsed) -->
  <div class="section-content" *ngIf="!isCollapsed()">
    <!-- Field list (always present, handles both empty and filled states) -->
    <app-field-list
      [fields]="fieldsSignal"
      [sectionId]="sectionIdSignal"
      (fieldsReordered)="onFieldsReordered($event)"
      (fieldRequiredToggled)="onFieldRequiredToggled($event)"
      (fieldPropertiesEdit)="onFieldPropertiesEdit($event)"
      (fieldPermissionSet)="onFieldPermissionSet($event)"
      (fieldDeleted)="onFieldDeleted($event)"
      (fieldLabelChanged)="onFieldLabelChanged($event)"
      (quickAddField)="onQuickAddField($event)">
    </app-field-list>
  </div>

  <!-- Drag preview -->
  <div class="section-drag-preview" *cdkDragPreview>
    <div class="preview-content">
      <mat-icon>drag_indicator</mat-icon>
      <span>{{ sectionSignal().title }}</span>
      <span class="field-count" *ngIf="hasFields()">
        ({{ sectionSignal().fields.length }} {{ 'DYNAMIC_LAYOUT_BUILDER.FIELD.FIELDS' | translate }})
      </span>
    </div>
  </div>
</div>
