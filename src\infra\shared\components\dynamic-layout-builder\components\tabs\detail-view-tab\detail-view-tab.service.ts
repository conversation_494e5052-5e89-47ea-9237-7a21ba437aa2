import { inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FlashMessageService } from '@core/services/flash_message.service';
import {
  DetailViewConfig,
  DetailViewSection
} from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-builder.dto';
import {
  StateChangeEventPayload,
  StateChangeEventType,
  WidgetReorderEventData
} from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-builder.model';
import { DynamicLayoutConfigStateService } from '../../../services/dynamic-layout-config-state.service';
import { DynamicLayoutBuilderStateService } from '../../../services/dynamic-layout-builder-state.service';
import { moveItemInArray } from '@angular/cdk/drag-drop';

/**
 * DetailViewTabService - Non-injectable service cho Detail View tab business logic
 *
 *
 * Responsibilities:
 * - Widget management business logic (add, remove, reorder)
 * - Section management business logic cho Detail View
 * - Validation và business rules
 * - Data transformation
 * - Integration: Sử dụng CoreLayoutBuilderService cho core operations
 *
 * Usage:
 * - Instantiate trong component: `private detailViewTabService = new DetailViewTabService();`
 * - Mỗi component có instance riêng để tránh state sharing
 * - State được quản lý bởi DynamicLayoutConfigStateService
 */
export class DetailViewTabService {
  constructor(
    private configStateService: DynamicLayoutConfigStateService,
    private stateService: DynamicLayoutBuilderStateService,
    private flashMessageService: FlashMessageService,
    private translateService: TranslateService
  ) {}

  // ==================== WIDGET MANAGEMENT BUSINESS LOGIC ====================

  /**
   * Delete widget from section
   * Business Logic: Remove widget và update order cho remaining widgets
   */
  deleteWidget(sectionId: string, widgetId: string) {
    if (!this.configStateService.detailViewConfig()) {
      return ;
    }

    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.DELETED')
    );

    return this.updateConfig(
      this.configStateService.detailViewTabSections().map(section => {
        if (section._id === sectionId) {
          const updatedWidgets = section.widgets
            .filter(w => w._id !== widgetId)
            .map((widget, index) => ({
              ...widget,
              order: index + 1
            }));

          return {
            ...section,
            widgets: updatedWidgets
          };
        }
        return section;
      }), 
      StateChangeEventType.DV_WIDGET_DELETED
    );
  }

  /**
   * Reorder widgets within section
   * Business Logic: Update widget positions và orders
   */
  reorderWidgets(reorderData: WidgetReorderEventData) {
    if (!this.configStateService.detailViewConfig()) {
      return;
    }

    moveItemInArray(reorderData.widgets, reorderData.oldIndex!, reorderData.newIndex!);


    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.UPDATED')
    );

    return this.updateConfig(
      this.configStateService.detailViewTabSections().map(section => {
        if (section._id === reorderData.sectionId) {
          return {
            ...section,
            widgets: reorderData.widgets
          };
        }
        return section;
      }),
      StateChangeEventType.DV_WIDGET_REORDERED, 
      reorderData
    );
  }

  private updateConfig(updatedSections: DetailViewConfig['sections'], event?: StateChangeEventType, payload?: StateChangeEventPayload) {
    this.configStateService.updateDetailViewTabData({
      ...this.configStateService.detailViewConfig()!,
      sections: updatedSections
    });
    this.configStateService.setUnsavedChanges(true);
    this.triggerAutoSave();

    if(event) {
      this.stateService.trackEvent(event, payload);
    }
  }

  

  private triggerAutoSave(): void {
    // // ✅ REFACTORED: Lấy sections từ configStateService
    // const currentSections = this.configStateService.getDetailViewTabSections();

    // if (currentSections.length > 0) {
    //   // ✅ NEW: Check autoSave config trước khi auto-save
    //   const currentState = this.configStateService.getCurrentState();
    //   const autoSaveEnabled = currentState?.currentLayout?.autoSave ?? true;

    //   if (!autoSaveEnabled) {
    //     console.log('⏸️ DetailViewTab: Auto-save disabled, skipping auto-save');
    //     return;
    //   }

    //   console.log('💾 DetailViewTab: Triggering auto-save');

    //   // ✅ REFACTORED: Tạo detailViewConfig từ sections hiện tại
    //   const detailViewConfig: DetailViewConfig = {
    //     title: 'Detail View',
    //     sections: currentSections
    //   };

    //   // Cập nhật data trong DynamicLayoutConfigStateService
    //   this.configStateService.updateDetailViewTabData(detailViewConfig);
    // }
  }
}
