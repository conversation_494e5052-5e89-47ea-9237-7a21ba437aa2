import { Section, QuickCreateConfig } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-builder.dto';
import { Field } from '@domain/entities/field.entity';
import { CoreLayoutBuilderService } from '../../../services/core-layout-builder.service';
import { DynamicLayoutConfigStateService } from '../../../services/dynamic-layout-config-state.service';
import { TranslateService } from '@ngx-translate/core';
import { FlashMessageService } from '@/core/services/flash_message.service';
import { moveItemInArray } from '@angular/cdk/drag-drop';
import { getFieldTypeLabel } from '../../../constants/field-types.const';

/**
 * QuickCreateTabService - Non-injectable service cho Quick Create Tab business logic
 *
 * ✅ REFACTORED: Removed @Injectable() decorator - instantiate with 'new QuickCreateTabService()'
 * ✅ ADDED: Instance riêng của CoreLayoutBuilderService để tránh shared state conflicts
 *
 * Responsibilities:
 * - Section initialization và configuration logic
 * - Data transformation (QuickCreateField -> Field)
 * - Field addition và reordering business logic
 * - State persistence và loading logic
 * - Validation và business rules
 * - Integration: Sử dụng CoreLayoutBuilderService cho core operations
 *
 * Usage:
 * - Instantiate trong component: `private quickCreateTabService = new QuickCreateTabService();`
 * - Mỗi component có instance riêng để tránh state sharing
 */
export class QuickCreateTabService {
  /**
   * Instance riêng của CoreLayoutBuilderService
   * Mỗi QuickCreateTabService có instance riêng để tránh shared state conflicts
   */
  private coreLayoutBuilderService = new CoreLayoutBuilderService();

  constructor(
    private configStateService: DynamicLayoutConfigStateService,
    private flashMessageService: FlashMessageService,
    private translateService: TranslateService
  ) {}

  /**
   * Check if field is duplicate in Quick Create section
   * Business Logic: Duplicate validation
   */
  isFieldDuplicate(field: Field, currentSection: Section | null): boolean {
    if (!currentSection) {
      return false;
    }

    return currentSection.fields.some(existingField => 
      existingField.type === field.type && existingField.label === field.label
    );
  }

  /**
   * Add field to Quick Create section
   * ✅ REFACTORED: Sử dụng CoreLayoutBuilderService để tạo field với ID unique
   */
  addFieldToSection(field: Field) {
    if (!field) {
      console.error('❌ No field data found in drag event');
      return;
    }

    const currentSection = this.configStateService.quickCreateSection()!;
    if(!currentSection) {
      return; 
    }
    
    try {
      const isDuplicate = this.isFieldDuplicate(field, currentSection);

      if (isDuplicate) {
        console.log(`❌ Preventing duplicate field: ${field.label}`);
        this.flashMessageService.warning(
          `Trường "${field.label}" đã được thêm vào Quick Create layout`
        );
        return;
      }

      // Add field to Quick Create section
      const section = this.configStateService.quickCreateSection();
      if (!section) {
        this.flashMessageService.error('Không tìm thấy Quick Create section');
        return;
      }
      
      // Merge with original field properties
      const mergedField: Field = {
        ...field,
        // _id: newField._id, // Keep CoreLayoutBuilderService generated ID
        // id: newField._id, // Removed - using _id only   // Keep CoreLayoutBuilderService generated ID
        order: currentSection.fields.length + 1,
      };

  
      this.updateSection({
        ...currentSection,
        fields: [...currentSection.fields, mergedField]
      })
      
      this.flashMessageService.success(
        `Đã thêm trường "${getFieldTypeLabel(field.type, this.translateService)}" vào Quick Create layout`
      );
    } catch (error) {
      console.error('Error adding field:', error);
      this.flashMessageService.error('Có lỗi xảy ra khi thêm trường');
    }
  }

  
  /**
   * Reorder fields in Quick Create section
   * ✅ REFACTORED: Sử dụng CoreLayoutBuilderService để xử lý field reordering
   */
  reorderFieldsInSection(oldIndex: number, newIndex: number) {
    const section = this.configStateService.quickCreateSection();
    if (!section) return;

    // Business logic: Reorder fields in section using CDK moveItemInArray
    const updatedFields = [...section.fields];
    moveItemInArray(updatedFields, oldIndex, newIndex);


    this.updateSection({
      ...section,
      fields: updatedFields
    })

    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.UPDATED')
    );
  }

  deleteField(field: Field): void {
    const section = this.configStateService.quickCreateSection();
    if (!section) return;

    this.updateSection(
      this.coreLayoutBuilderService.removeFieldFromSection(
        [section], 
        section._id, 
        field._id!
      )?.[0]
    );

    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.GENERAL.DELETED')
    );
  }

  updateSection(updatedSection: Section) {
    if (!updatedSection) {
      console.error('❌ No updated section provided');
      return;
    }
    this.configStateService.setUnsavedChanges(true);
    this.configStateService.setQuickCreateSection(updatedSection);
    this.triggerAutoSave();
  }

  private triggerAutoSave(): void {
    // const quickCreateSection = this.configStateService.getQuickCreateSection();

    // if (quickCreateSection) {
    //   // ✅ NEW: Check autoSave config trước khi auto-save
    //   const currentState = this.configStateService.getCurrentState();
    //   const autoSaveEnabled = currentState?.currentLayout?.autoSave ?? true;

    //   if (!autoSaveEnabled) {
    //     // console.log('⏸️ QuickCreateTab: Auto-save disabled, skipping auto-save');
    //     return;
    //   }

    //   // console.log('💾 QuickCreateTab: Triggering auto-save');

    //   // Cập nhật data trong DynamicLayoutConfigStateService
    //   this.configStateService.updateQuickCreateTabData(quickCreateSection);

    //   // ✅ MERGED: Save state logic từ autoSaveState() method
    //   // setTimeout(() => this.saveTabState(), 100);
    // }
  }
}
