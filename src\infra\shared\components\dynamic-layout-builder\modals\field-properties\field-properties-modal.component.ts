import { Component, signal, computed, ChangeDetectionStrategy, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSelectModule } from '@angular/material/select';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { Field } from '@domain/entities/field.entity';
import { StrictModalComponent } from '@shared/components/standard-dialog/interfaces/modal-component.interface';

export interface FieldPropertiesData {
  field: Field;
  availableSearchModules?: Array<{
    _id: string;
    name: string;
  }>;
}

/**
 * Kiểu dữ liệu trả về từ modal
 */
export type FieldPropertiesModalResult = Field | undefined;

/**
 * Component modal chỉnh sửa thuộc tính field
 */
@Component({
  selector: 'app-field-properties',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatSelectModule,
    MatExpansionModule,
    MatIconModule,
    MatMenuModule,
    DragDropModule,
    TranslateModule
  ],
  templateUrl: './field-properties-modal.component.html',
  styleUrls: ['./field-properties-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FieldPropertiesModalComponent implements OnDestroy, StrictModalComponent<FieldPropertiesData, FieldPropertiesModalResult> {
  /**
   * Dữ liệu đầu vào cho modal
   */
  data!: FieldPropertiesData;

  /**
   * Form chính để quản lý tất cả input
   */
  form: FormGroup;

  /**
   * Trạng thái hiển thị tooltip input
   */
  showTooltipInput = signal(false);

  /**
   * Trạng thái chế độ bulk add cho picklist
   */
  isBulkMode = signal(false);

  /**
   * Nội dung bulk text cho picklist
   */
  bulkText = signal('');

  /**
   * Tên hiển thị của field type
   */
  fieldTypeName = computed(() => {
    if (!this.data?.field?.type) return '';
    const fieldType = this.data.field.type;
    return this.translateService.instant(`EDIT_FIELD_PROPERTIES.FIELD_TYPE_${fieldType.toUpperCase().replace('-', '_')}`);
  });

  /**
   * Kiểm tra xem field có hỗ trợ unique constraint không
   */
  supportsUnique = computed(() => {
    if (!this.data?.field?.type) return false;
    const supportedTypes = ['text', 'email', 'phone', 'url'];
    return supportedTypes.includes(this.data.field.type);
  });

  /**
   * Danh sách tùy chọn cho picklist (reactive signal)
   */
  picklistOptions = signal<string[]>([]);

  /**
   * Subscription để quản lý memory leaks
   */
  private subscriptions = new Subscription();

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService
  ) {
    // Khởi tạo form với dữ liệu mặc định
    this.form = this.createForm();

    // Theo dõi thay đổi tooltip checkbox
    const tooltipCheckboxSub = this.form.get('showTooltip')?.valueChanges.subscribe(value => {
      this.showTooltipInput.set(value);
      if (!value) {
        this.form.get('tooltip')?.setValue('');
      }
    });
    if (tooltipCheckboxSub) {
      this.subscriptions.add(tooltipCheckboxSub);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Tạo form dựa trên field data
   */
  private createForm(): FormGroup {
    const field = this.data?.field || {} as Field;

    return this.fb.group({
      // Các trường chung
      label: [field.label || '', [Validators.required, Validators.maxLength(255)]],
      isPublic: [field.isPublic || false],
      isRequired: [field.isRequired || false],
      unique: [(field.constraints as any)?.unique || false],
      showTooltip: [!!field.tooltip],
      tooltip: [field.tooltip || '', [Validators.maxLength(500)]],

      // Các trường đặc thù sẽ được thêm động
      ...this.createFieldSpecificControls()
    });
  }

  /**
   * Tạo các controls đặc thù cho từng loại field
   */
  private createFieldSpecificControls(): { [key: string]: any } {
    const field = this.data?.field || {} as Field;
    const constraints = field.constraints as any || {};
    const controls: { [key: string]: any } = {};

    if (!field.type) {
      return controls;
    }

    switch (field.type) {
      case 'text':
      case 'phone':
      case 'url':
        controls.maxLength = [constraints.maxLength || 255, [Validators.min(1), Validators.max(255)]];
        break;

      case 'number':
        controls.maxDigits = [constraints.maxDigits || 9, [Validators.min(1), Validators.max(9)]];
        break;

      case 'textarea':
        controls.textType = [constraints.textType || 'small', Validators.required];
        break;

      case 'picklist':
      case 'multi-picklist':
        controls.defaultValue = [constraints.defaultValue || (field.type === 'multi-picklist' ? [] : '')];
        controls.sortOrder = [constraints.sortOrder || 'input'];
        break;

      case 'search':
        controls.searchModule = [constraints.searchModule || 'sales_quotes', Validators.required];
        break;

      case 'user':
        controls.userType = [constraints.userType || 'single', Validators.required];
        break;

      case 'file':
        controls.allowMultipleFiles = [constraints.allowMultipleFiles || false];
        controls.maxFiles = [constraints.maxFiles || 1, [Validators.min(1), Validators.max(5)]];
        break;

      case 'image':
        controls.maxImages = [constraints.maxImages || 1, Validators.required];
        break;

      case 'currency':
        controls.maxDigits = [constraints.maxDigits || 9, [Validators.min(1), Validators.max(16)]];
        controls.decimalPlaces = [constraints.decimalPlaces || 2, [Validators.min(0), Validators.max(4)]];
        controls.rounding = [constraints.rounding || 'normal'];
        break;

      case 'decimal':
        controls.maxDigits = [constraints.maxDigits || 16, [Validators.min(1), Validators.max(16)]];
        controls.decimalPlaces = [constraints.decimalPlaces || 2, [Validators.min(0), Validators.max(8)]];
        controls.useNumberSeparator = [constraints.useNumberSeparator || false];
        break;

      case 'checkbox':
        controls.enableByDefault = [constraints.enableByDefault || false];
        break;
    }

    return controls;
  }

  /**
   * Kiểm tra xem field có phải là picklist không
   */
  isPicklistField(): boolean {
    if (!this.data?.field?.type) return false;
    return this.data.field.type === 'picklist' || this.data.field.type === 'multi-picklist';
  }

  /**
   * Lấy constraints của picklist field
   */
  private getPicklistConstraints() {
    if (!this.isPicklistField()) return null;
    return (this.data?.field?.constraints as any) || {};
  }

  /**
   * Thêm option mới vào picklist
   */
  addPicklistOption(insertIndex?: number): void {
    const currentOptions = this.picklistOptions();
    const newOptions = [...currentOptions];

    if (insertIndex !== undefined) {
      newOptions.splice(insertIndex + 1, 0, '');
    } else {
      newOptions.push('');
    }

    this.picklistOptions.set(newOptions);
  }

  /**
   * Xóa option khỏi picklist
   */
  removePicklistOption(index: number): void {
    const currentOptions = this.picklistOptions();
    const newOptions = currentOptions.filter((_, i) => i !== index);
    this.picklistOptions.set(newOptions);
  }

  /**
   * Cập nhật option trong picklist
   */
  updatePicklistOption(index: number, value: string): void {
    const currentOptions = this.picklistOptions();
    const newOptions = [...currentOptions];
    newOptions[index] = value;
    this.picklistOptions.set(newOptions);
  }

  /**
   * Xử lý kéo thả picklist options
   */
  onPicklistDrop(event: CdkDragDrop<string[]>): void {
    const currentOptions = this.picklistOptions();
    const newOptions = [...currentOptions];
    moveItemInArray(newOptions, event.previousIndex, event.currentIndex);
    this.picklistOptions.set(newOptions);
  }

  /**
   * Chuyển sang chế độ bulk add
   */
  enterBulkMode(): void {
    this.isBulkMode.set(true);
    this.bulkText.set(this.picklistOptions().join('\n'));
  }

  /**
   * Lưu bulk options
   */
  saveBulkOptions(): void {
    const text = this.bulkText();
    const options = text
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);

    this.picklistOptions.set(options);
    this.exitBulkMode();
  }

  /**
   * Thoát chế độ bulk add
   */
  exitBulkMode(): void {
    this.isBulkMode.set(false);
    this.bulkText.set('');
  }

  /**
   * TrackBy function cho picklist options
   */
  trackByOption(index: number, option: string): string {
    return `${index}-${option}`;
  }

  /**
   * Xử lý khi nhấn nút Save
   */
  onSave(): void {
    if (this.form.valid) {
      const updatedField = this.buildUpdatedField();
      this.close();
    }
  }

  /**
   * Xử lý khi nhấn nút Cancel
   */
  onCancel(): void {
    this.close();
  }

  /**
   * Đóng modal - Không cần thiết với ResponsiveModalService pattern
   * Modal sẽ tự động đóng khi getModalResult() được gọi
   */
  private close(): void {
    // ResponsiveModalService sẽ tự động xử lý việc đóng modal
    // Không cần gọi dialogRef.close() hay bottomSheetRef.dismiss()
  }

  /**
   * Xây dựng Field đã được cập nhật
   */
  private buildUpdatedField(): Field {
    const formValue = this.form.value;
    const field = { ...(this.data?.field || {} as Field) };

    // Cập nhật các trường chung
    field.label = formValue.label;
    field.isPublic = formValue.isPublic;
    field.isRequired = formValue.isRequired;
    field.tooltip = formValue.showTooltip ? formValue.tooltip : undefined;

    // Khởi tạo constraints nếu chưa có
    if (!field.constraints) {
      field.constraints = {};
    }

    // Cập nhật unique constraint nếu field hỗ trợ
    if (this.supportsUnique()) {
      (field.constraints as any).unique = formValue.unique;
    }

    // Cập nhật constraints đặc thù theo loại field
    this.updateFieldSpecificConstraints(field, formValue);

    return field;
  }

  /**
   * Kiểm tra xem field có tùy chọn đặc thù không
   */
  hasFieldSpecificOptions(): boolean {
    const fieldType = this.data.field.type;
    const typesWithOptions = [
      'text', 'phone', 'url', 'number', 'textarea',
      'picklist', 'multi-picklist', 'search', 'user',
      'file', 'image', 'currency', 'decimal', 'checkbox'
    ];
    return typesWithOptions.includes(fieldType);
  }

  /**
   * Cập nhật constraints đặc thù cho từng loại field
   */
  private updateFieldSpecificConstraints(field: Field, formValue: Record<string, unknown>): void {
    const constraints = field.constraints as Record<string, unknown>;

    switch (field.type) {
      case 'text':
      case 'phone':
      case 'url':
        constraints.maxLength = formValue.maxLength;
        break;

      case 'number':
        constraints.maxDigits = formValue.maxDigits;
        break;

      case 'textarea':
        constraints.textType = formValue.textType;
        // Set maxLength based on textType
        const lengthMap = { small: 2000, large: 32000, rich: 50000 };
        constraints.maxLength = lengthMap[formValue.textType as keyof typeof lengthMap];
        break;

      case 'picklist':
      case 'multi-picklist':
        constraints.picklistValues = this.picklistOptions();
        constraints.defaultValue = formValue.defaultValue;
        constraints.sortOrder = formValue.sortOrder;
        break;

      case 'search':
        constraints.searchModule = formValue.searchModule;
        break;

      case 'user':
        constraints.userType = formValue.userType;
        break;

      case 'file':
        constraints.allowMultipleFiles = formValue.allowMultipleFiles;
        constraints.maxFiles = formValue.allowMultipleFiles ? formValue.maxFiles : 1;
        break;

      case 'image':
        constraints.maxImages = formValue.maxImages;
        break;

      case 'currency':
        constraints.maxDigits = formValue.maxDigits;
        constraints.decimalPlaces = formValue.decimalPlaces;
        constraints.rounding = formValue.rounding;
        break;

      case 'decimal':
        constraints.maxDigits = formValue.maxDigits;
        constraints.decimalPlaces = formValue.decimalPlaces;
        constraints.useNumberSeparator = formValue.useNumberSeparator;
        break;

      case 'checkbox':
        constraints.enableByDefault = formValue.enableByDefault;
        if (formValue.enableByDefault) {
          (field as any).value = true;
        }
        break;
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): FieldPropertiesModalResult {
    if (!this.form.valid) {
      return undefined;
    }

    return this.buildUpdatedField();
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return this.form.valid;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: FieldPropertiesData): void {
    this.data = data;

    // Tạo lại form với dữ liệu mới
    this.form = this.createForm();

    // Cập nhật trạng thái tooltip
    this.showTooltipInput.set(!!data.field.tooltip);

    // Cập nhật picklist options nếu có
    if (this.isPicklistField()) {
      const options = this.getPicklistConstraints()?.picklistValues || [];
      this.picklistOptions.set([...options]);
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào input đầu tiên khi modal mở
    setTimeout(() => {
      const firstInput = document.querySelector('input[formControlName="label"]') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
