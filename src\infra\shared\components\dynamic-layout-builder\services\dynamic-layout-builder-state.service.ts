import { signal, computed } from '@angular/core';
import { BehaviorSubject, Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
// Import các interface khác từ model cũ (giữ lại để tham khảo)
import {
  DynamicLayoutBuilderState,
  StateChangeEvent,
  StateChangeEventType,
  StateChangeEventPayload,
  UIState,
  CreateTabStateExtended,
  QuickCreateTabStateExtended,
  DetailViewTabStateExtended,
  DragOperation,
  CreateTabState,
  DetailViewTabState,
  TabState
} from '../models/dynamic-layout-builder.model';
import { Section } from '../models/dynamic-layout-builder.dto';

import { FlashMessageService } from '@core/services/flash_message.service';
import { TranslateService } from '@ngx-translate/core';

/**
 * DynamicLayoutBuilderStateService - Centralized state management cho Dynamic Layout Builder
 *
 * ✅ REFACTORED: Non-injectable service for centralized state management
 * ✅ CHANGED: Removed @Injectable() decorator - instantiate with 'new DynamicLayoutBuilderStateService()'
 * ✅ CHANGED: Dependencies được inject qua constructor thay vì inject()
 *
 * Responsibilities:
 * - Single source of truth cho toàn bộ DynamicLayoutBuilderConfig state
 * - Event tracking và notification cho tất cả state changes
 * - Real-time synchronization giữa các components
 * - Integration với existing services (DynamicLayoutBuilderService, LayoutSelectorService)
 *
 * Architecture:
 * - Non-injectable service với instance riêng cho mỗi component
 * - BehaviorSubject cho reactive state management
 * - Angular Signals cho reactive UI
 * - Event-driven architecture với Observable streams
 * - Type-safe state updates và queries
 * - Error handling và logging
 *
 * Usage:
 * - Instantiate trong component: `private stateService = new DynamicLayoutBuilderStateService(flashMessageService, translateService);`
 * - Mỗi component có instance riêng để tránh shared state conflicts
 */
export class DynamicLayoutBuilderStateService {

  // ==================== DEPENDENCIES ====================

  private flashMessageService: FlashMessageService;
  private translateService: TranslateService;

  // ==================== STATE MANAGEMENT ====================
  
  // Core state streams
  private stateSubject = new BehaviorSubject<DynamicLayoutBuilderState | null>(null);
  public state$ = this.stateSubject.asObservable();
  
  // Event tracking
  private eventSubject = new Subject<StateChangeEvent>();
  public events$ = this.eventSubject.asObservable();
  
  // Destroy subject cho subscription cleanup
  private destroy$ = new Subject<void>();
  
  // Subscription management
  private subscriptions = new Subscription();

  // ==================== DERIVED OBSERVABLES ====================
  // Chỉ giữ lại basic observables cho UI state

  // ==================== REACTIVE SIGNALS ====================
  
  // Core state signal
  state = signal<DynamicLayoutBuilderState | null>(null);
  
  // Computed signals cho UI
  isLoading = computed(() => this.state()?.isLoading || false);
  isPreviewMode = computed(() => this.state()?.isPreviewMode || false);
  selectedTabIndex = computed(() => this.state()?.selectedTabIndex || 0);
  
  // Tab state computed signals
  createTabState = computed(() => this.state()?.createTabState || null);
  quickCreateTabState = computed(() => this.state()?.quickCreateTabState || null);
  detailViewTabState = computed(() => this.state()?.detailViewTabState || null);
  


  // ==================== CONSTRUCTOR & LIFECYCLE ====================

  /**
   * ✅ REFACTORED: Constructor nhận dependencies thay vì inject()
   */
  constructor(
    flashMessageService: FlashMessageService,
    translateService: TranslateService
  ) {
    this.flashMessageService = flashMessageService;
    this.translateService = translateService;

    // console.log('🏗️ DynamicLayoutBuilderStateService: Service được khởi tạo');
    this.initializeService();
  }

  /**
   * Manual cleanup method - được gọi từ component's ngOnDestroy
   * Angular service KHÔNG có lifecycle hooks tự động!
   */
  cleanup(): void {
    // console.log('🧹 DynamicLayoutBuilderStateService: Manual cleanup called');
    this.destroy$.next();
    this.destroy$.complete();
    this.subscriptions.unsubscribe();
  }


  // ==================== INITIALIZATION ====================
  
  /**
   * Khởi tạo service và sync với existing services
   */
  private initializeService(): void {
    // Sync state signal với BehaviorSubject
    this.subscriptions.add(
      this.state$.pipe(takeUntil(this.destroy$)).subscribe(state => {
        this.state.set(state);
      })
    );
    
    // Note: LayoutSelectorService is deprecated, không cần subscribe
    // Layout changes are now handled directly through this service
    
    // console.log('✅ DynamicLayoutBuilderStateService: Service initialized');
  }

  /**
   * Initialize state với basic UI state (không cần config)
   */
  initialize(): void {
    // console.log('🚀 DynamicLayoutBuilderStateService: Initializing UI state');

    try {
      const initialState = this.createInitialUIState();
      this.stateSubject.next(initialState);

      // Track initialization event
      this.trackEvent(StateChangeEventType.LAYOUT_SWITCHED, null);

      // console.log('✅ UI State initialized successfully');
    } catch (error) {
      // console.error('❌ Error initializing UI state:', error);
      this.flashMessageService.error(
        this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.STATE.INIT_ERROR')
      );
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Tạo initial UI state (không phụ thuộc vào config)
   * ✅ REFACTORED: Thay thế null as any bằng null với proper typing
   */
  private createInitialUIState(): DynamicLayoutBuilderState {
    return {
      config: null!, // Sẽ được inject từ DynamicLayoutConfigStateService - sử dụng null! thay vì null as any
      currentLayoutId: '', // Sẽ được sync từ DynamicLayoutConfigStateService
      selectedTabIndex: 0,
      createTabState: this.createInitialCreateTabState(),
      quickCreateTabState: this.createInitialQuickCreateTabState(),
      detailViewTabState: this.createInitialDetailViewTabState(),
      isLoading: false,
      isPreviewMode: false,
      lastUpdated: new Date().toISOString(),
      version: 1,
      instanceId: this.generateInstanceId()
    };
  }


  
  /**
   * Tạo initial Create tab state (empty state)
   */
  private createInitialCreateTabState(): CreateTabStateExtended {
    return {
      sections: [],
      sectionCount: 0,
      fieldCount: 0,
      lastUpdated: new Date().toISOString(),
      totalSectionOperations: 0,
      totalFieldOperations: 0
    };
  }

  /**
   * Tạo initial Quick Create tab state (empty state)
   */
  private createInitialQuickCreateTabState(): QuickCreateTabStateExtended {
    const quickCreateSection: Section = {
      _id: this.generateSectionId(),
      id: this.generateSectionId(),
      title: this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEFAULT_VALUES.QUICK_CREATE_SECTION_TITLE'),
      fields: []
    };

    return {
      quickCreateSection,
      fieldCount: 0,
      autoSaveEnabled: false,
      autoSaveInterval: 30000,
      lastUpdated: new Date().toISOString(),
      totalFieldOperations: 0
    };
  }

  /**
   * Tạo initial Detail View tab state (empty state)
   */
  private createInitialDetailViewTabState(): DetailViewTabStateExtended {
    const detailViewConfig = {
      title: this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.DEFAULT_VALUES.DETAIL_VIEW_TITLE'),
      sections: []
    };

    return {
      detailViewConfig,
      sectionCount: 0,
      widgetCount: 0,
      lastUpdated: new Date().toISOString(),
      totalWidgetOperations: 0,
      totalSectionOperations: 0
    };
  }
  
  /**
   * Utility methods
   */
  private generateInstanceId(): string {
    return `dlb-state-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  private generateSectionId(): string {
    return `section-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }
  
  // handleLayoutChange method removed - layout changes are now handled directly through switchLayout()

  // ==================== PUBLIC API METHODS ====================
  
  /**
   * Track state change event
   * ✅ REFACTORED: Thay thế any bằng StateChangeEventPayload
   */
  trackEvent(eventType: StateChangeEventType, payload: StateChangeEventPayload): void {
    const currentState = this.stateSubject.value;
    if (!currentState) return;
    
    const event: StateChangeEvent = {
      type: eventType,
      payload,
      timestamp: new Date().toISOString(),
      layoutId: currentState.currentLayoutId,
      metadata: {
        version: currentState.version,
        instanceId: currentState.instanceId
      }
    };
    
    this.eventSubject.next(event);
    // console.log('📊 Event tracked:', eventType, payload);
  }
  
  /**
   * Get current state
   */
  getCurrentState(): DynamicLayoutBuilderState | null {
    return this.stateSubject.value;
  }
  
  /**
   * Get specific tab state
   */
  getTabState<T>(tabName: string): T | null {
    const state = this.stateSubject.value;
    if (!state) return null;

    switch (tabName) {
      case 'create':
        return state.createTabState as T;
      case 'quickCreate':
        return state.quickCreateTabState as T;
      case 'detailView':
        return state.detailViewTabState as T;
      default:
        return null;
    }
  }

  // ==================== STATE UPDATE METHODS ====================

  /**
   * Update Create tab state
   */
  updateCreateTabState(updates: Partial<CreateTabStateExtended>): void {
    const currentState = this.stateSubject.value;
    if (!currentState) {
      // console.warn('⚠️ Cannot update Create tab state: no current state');
      return;
    }

    const updatedCreateTabState: CreateTabStateExtended = {
      ...currentState.createTabState,
      ...updates,
      lastUpdated: new Date().toISOString()
    };

    const updatedState: DynamicLayoutBuilderState = {
      ...currentState,
      createTabState: updatedCreateTabState,
      lastUpdated: new Date().toISOString(),
      version: currentState.version + 1
    };

    this.stateSubject.next(updatedState);

    // Track update event
    this.trackEvent(StateChangeEventType.SECTION_ADDED, null);

    // console.log('✅ Create tab state updated:', updates);
  }

  /**
   * Update Quick Create tab state
   */
  updateQuickCreateTabState(updates: Partial<QuickCreateTabStateExtended>): void {
    const currentState = this.stateSubject.value;
    if (!currentState) {
      // console.warn('⚠️ Cannot update Quick Create tab state: no current state');
      return;
    }

    const updatedQuickCreateTabState: QuickCreateTabStateExtended = {
      ...currentState.quickCreateTabState,
      ...updates,
      lastUpdated: new Date().toISOString()
    };

    const updatedState: DynamicLayoutBuilderState = {
      ...currentState,
      quickCreateTabState: updatedQuickCreateTabState,
      lastUpdated: new Date().toISOString(),
      version: currentState.version + 1
    };

    this.stateSubject.next(updatedState);

    // Track update event
    this.trackEvent(StateChangeEventType.QC_SECTION_UPDATED, null);

    // console.log('✅ Quick Create tab state updated:', updates);
  }

  /**
   * Update Detail View tab state
   */
  updateDetailViewTabState(updates: Partial<DetailViewTabStateExtended>): void {
    const currentState = this.stateSubject.value;
    if (!currentState) {
      // console.warn('⚠️ Cannot update Detail View tab state: no current state');
      return;
    }

    const updatedDetailViewTabState: DetailViewTabStateExtended = {
      ...currentState.detailViewTabState,
      ...updates,
      lastUpdated: new Date().toISOString()
    };

    const updatedState: DynamicLayoutBuilderState = {
      ...currentState,
      detailViewTabState: updatedDetailViewTabState,
      lastUpdated: new Date().toISOString(),
      version: currentState.version + 1
    };

    this.stateSubject.next(updatedState);

    // Track update event
    this.trackEvent(StateChangeEventType.DV_CONFIG_UPDATED, null);

    // console.log('✅ Detail View tab state updated:', updates);
  }

  /**
   * Update UI state
   */
  updateUIState(updates: Partial<UIState>): void {
    const currentState = this.stateSubject.value;
    if (!currentState) {
      // console.warn('⚠️ Cannot update UI state: no current state');
      return;
    }

    const updatedState: DynamicLayoutBuilderState = {
      ...currentState,
      ...updates,
      lastUpdated: new Date().toISOString(),
      version: currentState.version + 1
    };

    this.stateSubject.next(updatedState);

    // Track UI state changes
    if (updates.selectedTabIndex !== undefined) {
      this.trackEvent(StateChangeEventType.TAB_CHANGED, updates.selectedTabIndex);
    }

    if (updates.isPreviewMode !== undefined) {
      this.trackEvent(StateChangeEventType.PREVIEW_MODE_TOGGLED, updates.isPreviewMode);
    }

    if (updates.isLoading !== undefined) {
      this.trackEvent(StateChangeEventType.LOADING_STATE_CHANGED, updates.isLoading);
    }

    // console.log('✅ UI state updated:', updates);
  }


}
