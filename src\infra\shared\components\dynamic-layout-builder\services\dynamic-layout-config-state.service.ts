import { BehaviorSubject, Observable } from 'rxjs';
import { signal, WritableSignal } from '@angular/core';
import { DynamicLayoutBuilderConfig, DynamicLayoutConfig } from '../models/dynamic-layout-config.model';
import { Section, DetailViewConfig, DetailViewSection, WidgetConfig } from '../models/dynamic-layout-builder.dto';
import { Field } from '@domain/entities/field.entity';
import { getFilteredFieldTypes } from '../constants/field-types.const';
import { toSignal } from '@angular/core/rxjs-interop';

/**
 * Interface cho state của DynamicLayoutConfigStateService
 * ✅ REFACTORED: availableFieldTypes giờ được tính từ supportedFieldTypes filter
 */
export interface DynamicLayoutConfigState {
  /** Cấu hình toàn bộ layout builder */
  config: DynamicLayoutBuilderConfig;
  /** Layout hiện tại đang được chỉnh sửa */
  currentLayout: DynamicLayoutConfig;
  /** <PERSON>h sách các loại field có thể sử dụng - được filter từ DEFAULT_FIELD_TYPES */
  availableFieldTypes: Field[];
  /** Có thay đổi chưa được lưu không */
  hasUnsavedChanges: boolean;
  /** Thời gian thay đổi cuối cùng */
  lastModified: Date;
}

/**
 * Interface cho data confirm khi switch layout
 */
export interface LayoutSwitchConfirmData {
  /** Layout hiện tại (sẽ rời khỏi) */
  fromLayout: DynamicLayoutConfig;
  /** Layout đích (sẽ chuyển đến) */
  toLayout: DynamicLayoutConfig;
  /** Có thay đổi chưa được lưu không */
  hasUnsavedChanges: boolean;
}

/**
 * Service quản lý state tập trung cho Dynamic Layout Builder
 * - Không injectable, được tạo instance riêng cho mỗi DynamicLayoutBuilderComponent
 * - Quản lý currentLayout và auto-save changes
 * - Cung cấp Observable streams cho các component con
 */
export class DynamicLayoutConfigStateService {
  /** Subject quản lý toàn bộ state */
  private stateSubject = new BehaviorSubject<DynamicLayoutConfigState | null>(null);
  
  /** Subject quản lý layout hiện tại */
  private currentLayoutSubject = new BehaviorSubject<DynamicLayoutConfig | null>(null);

  /** Subject quản lý available field types */
  private availableFieldTypesSubject = new BehaviorSubject<Field[]>([]);

  /** Subject quản lý trạng thái unsaved changes */
  private unsavedChangesSubject = new BehaviorSubject<boolean>(false);

  /** Subject để emit confirm switch requests */
  private confirmSwitchSubject = new BehaviorSubject<LayoutSwitchConfirmData | null>(null);

  // ==================== QUICK CREATE SIGNALS ====================

  /** Signal chứa danh sách các sections có thể dùng cho Quick Create */
  private _quickCreateFieldSections: WritableSignal<Section[]> = signal([]);

  /** Signal chứa section hiện tại được chọn cho Quick Create */
  private _quickCreateSection: WritableSignal<Section | null> = signal(null);

  // ==================== CREATE TAB SIGNALS ====================

  /** Signal chứa danh sách sections cho Create tab */
  private _createTabSections: WritableSignal<Section[]> = signal([]);

  /** Signal chứa available field types cho Create tab */
  private _createTabAvailableFieldTypes: WritableSignal<Field[]> = signal([]);

  // ==================== DETAIL VIEW TAB SIGNALS ====================

  /** Signal chứa danh sách sections cho Detail View tab */
  private _detailViewTabSections: WritableSignal<DetailViewSection[]> = signal([]);

  /** Signal chứa available widget types cho Detail View tab */
  private _detailViewTabAvailableWidgetTypes: WritableSignal<WidgetConfig[]> = signal([]);

  /** Signal chứa current detail view config */
  private _detailViewConfig: WritableSignal<DetailViewConfig | null> = signal(null);

  // ==================== PUBLIC OBSERVABLES ====================
  
  /** Observable cho toàn bộ state */
  public readonly state$: Observable<DynamicLayoutConfigState | null> = this.stateSubject.asObservable();
  
  /** Observable cho layout hiện tại */
  public readonly currentLayout$: Observable<DynamicLayoutConfig | null> = this.currentLayoutSubject.asObservable();

  /** Observable cho available field types */
  public readonly availableFieldTypes$: Observable<Field[]> = this.availableFieldTypesSubject.asObservable();

  /** Observable cho trạng thái unsaved changes */
  public readonly hasUnsavedChanges$: Observable<boolean> = this.unsavedChangesSubject.asObservable();

  /** Observable cho confirm switch requests */
  public readonly confirmSwitch$: Observable<LayoutSwitchConfirmData | null> = this.confirmSwitchSubject.asObservable();

  public state = toSignal(this.state$, { initialValue: null });
  public currentLayout = toSignal(this.currentLayout$, { initialValue: null });


  // ==================== QUICK CREATE SIGNAL GETTERS ====================

  /** Getter cho quickCreateFieldSections signal */
  public get quickCreateFieldSections() {
    return this._quickCreateFieldSections;
  }

  /** Getter cho quickCreateSection signal */
  public get quickCreateSection() {
    return this._quickCreateSection;
  }

  // ==================== CREATE TAB SIGNAL GETTERS ====================

  /** Getter cho createTabSections signal */
  public get createTabSections() {
    return this._createTabSections;
  }

  /** Getter cho createTabAvailableFieldTypes signal */
  public get createTabAvailableFieldTypes() {
    return this._createTabAvailableFieldTypes;
  }

  // ==================== DETAIL VIEW TAB SIGNAL GETTERS ====================

  /** Getter cho detailViewTabSections signal */
  public get detailViewTabSections() {
    return this._detailViewTabSections;
  }

  /** Getter cho detailViewTabAvailableWidgetTypes signal */
  public get detailViewTabAvailableWidgetTypes() {
    return this._detailViewTabAvailableWidgetTypes;
  }

  /** Getter cho detailViewConfig signal */
  public get detailViewConfig() {
    return this._detailViewConfig;
  }

  
  
  
  /**
   * Khởi tạo service với config ban đầu
   * ✅ REFACTORED: Loại bỏ availableFieldTypes parameter - tự động tính từ supportedFieldTypes
   * @param config - DynamicLayoutBuilderConfig từ parent component
   */
  public initialize(config: DynamicLayoutBuilderConfig): void {
    const availableFieldTypes = getFilteredFieldTypes(config.selectedLayout.supportedFieldTypes);

    const initialState: DynamicLayoutConfigState = {
      config: { ...config },
      currentLayout: { ...config.selectedLayout },
      availableFieldTypes: availableFieldTypes,
      hasUnsavedChanges: false,
      lastModified: new Date()
    };

    this.changeLayout(initialState.currentLayout);

    this.stateSubject.next(initialState);
    this.availableFieldTypesSubject.next(availableFieldTypes);
    this.unsavedChangesSubject.next(false);
  }
  
  // ==================== GETTER METHODS ====================
  
  /**
   * Lấy layout hiện tại (synchronous)
   * @returns DynamicLayoutConfig hiện tại hoặc null
   */
  public getCurrentLayout(): DynamicLayoutConfig | null {
    return this.currentLayoutSubject.value;
  }
  
  /**
   * Lấy toàn bộ state hiện tại (synchronous)
   * @returns DynamicLayoutConfigState hiện tại hoặc null
   */
  public getCurrentState(): DynamicLayoutConfigState | null {
    return this.stateSubject.value;
  }
  
  /**
   * Kiểm tra có thay đổi chưa được lưu không
   * @returns boolean
   */
  public hasUnsavedChanges(): boolean {
    return this.unsavedChangesSubject.value;
  }

  /**
   * Set dirty state khi có thay đổi chưa được lưu
   * Được gọi từ các tab components khi có trigger conditions
   */
  public setUnsavedChanges(hasChanges: boolean = true): void {
    // console.log('🔄 DynamicLayoutConfigStateService: Set unsaved changes:', hasChanges);

    const currentState = this.stateSubject.value;
    if (!currentState) {
      // console.warn('⚠️ DynamicLayoutConfigStateService: Không có state hiện tại');
      return;
    }

    const updatedState: DynamicLayoutConfigState = {
      ...currentState,
      hasUnsavedChanges: hasChanges,
      lastModified: new Date()
    };

    this.stateSubject.next(updatedState);
    this.unsavedChangesSubject.next(hasChanges);
  }

  // ==================== QUICK CREATE STATE MANAGEMENT ====================

  /**
   * Cập nhật danh sách sections có thể dùng cho Quick Create
   * @param sections - Danh sách sections từ Create tab
   */
  public setQuickCreateFieldSections(sections: Section[]): void {
    this._quickCreateFieldSections.set([...sections]);
  }

  /**
   * Cập nhật section hiện tại được chọn cho Quick Create
   * @param section - Section được chọn cho Quick Create
   */
  public setQuickCreateSection(section: Section | null): void {
    this._quickCreateSection.set(section ? { ...section } : null);
  }

  /**
   * Lấy danh sách sections có thể dùng cho Quick Create (synchronous)
   * @returns Section[] - Danh sách sections
   */
  public getQuickCreateFieldSections(): Section[] {
    return this._quickCreateFieldSections();
  }

  /**
   * Lấy section hiện tại được chọn cho Quick Create (synchronous)
   * @returns Section | null - Section hiện tại hoặc null
   */
  public getQuickCreateSection(): Section | null {
    return this._quickCreateSection();
  }

  /**
   * Lấy QuickCreateConfig từ layout hiện tại (synchronous)
   * @returns QuickCreateConfig | null - QuickCreateConfig hiện tại hoặc null
   */
  public getQuickCreateConfig(): any | null {
    const currentLayout = this.getCurrentLayout();
    return currentLayout?.quickCreateConfig || null;
  }

  /**
   * Lấy ID của layout hiện tại (synchronous)
   * @returns string | null - Layout ID hiện tại hoặc null
   */
  public getCurrentLayoutId(): string | null {
    const currentLayout = this.getCurrentLayout();
    return currentLayout?._id || null;
  }

  // ==================== CREATE TAB STATE MANAGEMENT ====================

  /**
   * Cập nhật danh sách sections cho Create tab
   * @param sections - Danh sách sections từ Create tab
   */
  public setCreateTabSections(sections: Section[]): void {
    // console.log('📝 DynamicLayoutConfigStateService: Cập nhật createTabSections:', sections.length);
    this._createTabSections.set([...sections]);
  }

  /**
   * Cập nhật available field types cho Create tab
   * @param fieldTypes - Danh sách field types có thể sử dụng
   */
  public setCreateTabAvailableFieldTypes(fieldTypes: Field[]): void {
    // console.log('📝 DynamicLayoutConfigStateService: Cập nhật createTabAvailableFieldTypes:', fieldTypes.length);
    this._createTabAvailableFieldTypes.set([...fieldTypes]);
  }

  /**
   * Lấy danh sách sections cho Create tab (synchronous)
   * @returns Section[] - Danh sách sections
   */
  public getCreateTabSections(): Section[] {
    return this._createTabSections();
  }

  /**
   * Lấy available field types cho Create tab (synchronous)
   * @returns Field[] - Danh sách field types
   */
  public getCreateTabAvailableFieldTypes(): Field[] {
    return this._createTabAvailableFieldTypes();
  }

  // ==================== DETAIL VIEW TAB STATE MANAGEMENT ====================

  /**
   * Cập nhật danh sách sections cho Detail View tab
   * @param sections - Danh sách sections từ Detail View tab
   */
  public setDetailViewTabSections(sections: DetailViewSection[]): void {
    this._detailViewTabSections.set([...sections]);
  }

  /**
   * Cập nhật available widget types cho Detail View tab
   * @param widgetTypes - Danh sách widget types có thể sử dụng
   */
  public setDetailViewTabAvailableWidgetTypes(widgetTypes: WidgetConfig[]): void {
    this._detailViewTabAvailableWidgetTypes.set([...widgetTypes]);
  }

  /**
   * Lấy danh sách sections cho Detail View tab (synchronous)
   * @returns DetailViewSection[] - Danh sách sections
   */
  public getDetailViewTabSections(): DetailViewSection[] {
    return this._detailViewTabSections();
  }

  /**
   * Lấy available widget types cho Detail View tab (synchronous)
   * @returns WidgetConfig[] - Danh sách widget types
   */
  public getDetailViewTabAvailableWidgetTypes(): WidgetConfig[] {
    return this._detailViewTabAvailableWidgetTypes();
  }

  /**
   * Cập nhật current detail view config
   * @param config - Detail view config hoặc null
   */
  public setDetailViewConfig(config: DetailViewConfig | null): void {
    this._detailViewConfig.set(config);
  }

  /**
   * Lấy current detail view config (synchronous)
   * @returns DetailViewConfig | null - Current detail view config
   */
  public getDetailViewConfig(): DetailViewConfig | null {
    return this._detailViewConfig();
  }
  
  // ==================== UPDATE METHODS ====================
  
  /**
   * Cập nhật layout hiện tại và trigger auto-save
   * @param layout - DynamicLayoutConfig mới
   */
  public updateCurrentLayout(layout: DynamicLayoutConfig): void {
    // console.log('💾 DynamicLayoutConfigStateService: Cập nhật currentLayout:', layout.title);
    
    const currentState = this.stateSubject.value;
    if (!currentState) {
      // console.warn('⚠️ DynamicLayoutConfigStateService: State chưa được khởi tạo');
      return;
    }
    
    const updatedState: DynamicLayoutConfigState = {
      ...currentState,
      currentLayout: { ...layout },
      hasUnsavedChanges: true,
      lastModified: new Date()
    };
    
    this.stateSubject.next(updatedState);
    this.currentLayoutSubject.next(updatedState.currentLayout);
    this.unsavedChangesSubject.next(true);
    
    // Auto-save ngay lập tức
    this.autoSave();
  }
  
  /**
   * Auto-save layout hiện tại vào config
   * Chỉ lưu trong memory, không persist xuống database
   */
  public autoSave(): void {
    const currentState = this.stateSubject.value;
    if (!currentState) return;
    
    // console.log('💾 DynamicLayoutConfigStateService: Auto-save layout:', currentState.currentLayout.title);
    
    // Tìm và cập nhật layout trong config.layouts
    const layoutIndex = currentState.config.layouts.findIndex(
      layout => layout._id === currentState.currentLayout._id
    );
    
    if (layoutIndex !== -1) {
      currentState.config.layouts[layoutIndex] = { ...currentState.currentLayout };
      
      // Cập nhật selectedLayout nếu đang chỉnh sửa layout được chọn
      if (currentState.config.selectedLayout._id === currentState.currentLayout._id) {
        currentState.config.selectedLayout = { ...currentState.currentLayout };
      }
      
      // Reset unsaved changes flag
      const savedState: DynamicLayoutConfigState = {
        ...currentState,
        hasUnsavedChanges: false
      };
      
      this.stateSubject.next(savedState);
      this.unsavedChangesSubject.next(false);
      
      // console.log('✅ DynamicLayoutConfigStateService: Auto-save hoàn thành');
    }
  }
  
  // ==================== LAYOUT SWITCHING METHODS ====================
  
  /**
   * Chuyển đổi sang layout khác
   * @param layoutId - ID của layout muốn chuyển đến
   * @returns Promise<boolean> - true nếu chuyển thành công, false nếu bị hủy
   */
  public async switchLayout(layoutId: string): Promise<boolean> {
    const currentState = this.stateSubject.value;
    if (!currentState) {
      // console.warn('⚠️ DynamicLayoutConfigStateService: State chưa được khởi tạo');
      return false;
    }
    
    const targetLayout = currentState.config.layouts.find(layout => layout._id === layoutId);
    if (!targetLayout) {
      // console.warn('⚠️ DynamicLayoutConfigStateService: Không tìm thấy layout với ID:', layoutId);
      return false;
    }
    
    // Nếu đang chuyển đến cùng layout hiện tại, không cần làm gì
    if (currentState.currentLayout._id === layoutId) {
      // console.log('ℹ️ DynamicLayoutConfigStateService: Đã ở layout này rồi:', layoutId);
      return true;
    }
    
    // Nếu có unsaved changes, cần confirm từ user
    if (currentState.hasUnsavedChanges) {
      const confirmData: LayoutSwitchConfirmData = {
        fromLayout: currentState.currentLayout,
        toLayout: targetLayout,
        hasUnsavedChanges: true
      };

      // console.log('⚠️ DynamicLayoutConfigStateService: Có unsaved changes, cần confirm:', confirmData);

      // Emit confirm request để parent component xử lý
      this.confirmSwitchSubject.next(confirmData);

      // Return false để ngăn switch, parent component sẽ handle confirmation
      return false;
    }
    
    // Thực hiện switch layout
    // console.log('🔄 DynamicLayoutConfigStateService: Chuyển từ layout', currentState.currentLayout.title, 'sang', targetLayout.title);

    // ✅ NEW: Tính lại availableFieldTypes cho layout mới
    const newAvailableFieldTypes = getFilteredFieldTypes(targetLayout.supportedFieldTypes);

    const switchedState: DynamicLayoutConfigState = {
      ...currentState,
      currentLayout: { ...targetLayout },
      availableFieldTypes: newAvailableFieldTypes,
      hasUnsavedChanges: false,
      lastModified: new Date()
    };

    this.changeLayout(switchedState.currentLayout);
    this.stateSubject.next(switchedState);
    this.availableFieldTypesSubject.next(newAvailableFieldTypes);
    this.unsavedChangesSubject.next(false);

    return true;
  }

  /**
   * Force switch layout without checking unsaved changes
   * Được gọi sau khi user đã confirm trong modal
   * @param layoutId - ID của layout muốn chuyển đến
   * @returns boolean - true nếu chuyển thành công
   */
  public forceSwitchLayout(layoutId: string): boolean {
    const currentState = this.stateSubject.value;
    if (!currentState) {
      // console.warn('⚠️ DynamicLayoutConfigStateService: State chưa được khởi tạo');
      return false;
    }

    const targetLayout = currentState.config.layouts.find(layout => layout._id === layoutId);
    if (!targetLayout) {
      // console.warn('⚠️ DynamicLayoutConfigStateService: Không tìm thấy layout với ID:', layoutId);
      return false;
    }

    // Thực hiện switch layout mà không check unsaved changes
    // console.log('🔄 DynamicLayoutConfigStateService: Force switching layout to:', targetLayout.title);

    // ✅ NEW: Tính lại availableFieldTypes cho layout mới
    const newAvailableFieldTypes = getFilteredFieldTypes(targetLayout.supportedFieldTypes);

    const switchedState: DynamicLayoutConfigState = {
      ...currentState,
      currentLayout: { ...targetLayout },
      availableFieldTypes: newAvailableFieldTypes,
      hasUnsavedChanges: false,
      lastModified: new Date()
    };

    this.stateSubject.next(switchedState);
    this.currentLayoutSubject.next(switchedState.currentLayout);
    this.availableFieldTypesSubject.next(newAvailableFieldTypes);
    this.unsavedChangesSubject.next(false);

    // ✅ NEW: Đồng bộ Quick Create signals với layout mới
    this._quickCreateFieldSections.set(targetLayout.sections || []);
    this._quickCreateSection.set(targetLayout.quickCreateConfig?.section || null);

    // ✅ NEW: Đồng bộ Create Tab signals với layout mới
    this._createTabSections.set(targetLayout.sections || []);
    this._createTabAvailableFieldTypes.set(newAvailableFieldTypes);

    // ✅ NEW: Đồng bộ Detail View Tab signals với layout mới
    this._detailViewTabSections.set(targetLayout.detailViewConfig?.sections || []);
    this._detailViewTabAvailableWidgetTypes.set([]); // TODO: Implement widget types
    this._detailViewConfig.set(targetLayout.detailViewConfig || null);

    // console.log('✅ DynamicLayoutConfigStateService: Force layout switch completed to:', targetLayout.title);
    return true;
  }

  // ==================== TAB-SPECIFIC UPDATE METHODS ====================
  
  /**
   * Cập nhật data từ Create Tab (sections)
   * @param sections - Danh sách sections mới
   * 
   */
  public updateCreateTabData(sections: Section[]): void {
    const currentLayout = this.getCurrentLayout();
    if (!currentLayout) return;


    const updatedLayout: DynamicLayoutConfig = {
      ...currentLayout,
      sections: [...sections]
    };
    
    this.setCreateTabSections(updatedLayout.sections);

    // ✅ NEW: Đồng bộ với Quick Create field sections
    this.setQuickCreateFieldSections(updatedLayout.sections);

    // ✅ NEW: Đồng bộ với Create Tab sections
    this.syncQuickCreateFieldsWithCreateTab();
    this.updateCurrentLayout(updatedLayout);
  }
  
  /**
   * Cập nhật data từ Quick Create Tab (single section)
   * @param section - Section của Quick Create
   */
  public updateQuickCreateTabData(section: Section): void {
    const currentLayout = this.getCurrentLayout();
    if (!currentLayout) return;

    // console.log('📝 DynamicLayoutConfigStateService: Cập nhật Quick Create Tab data');

    const updatedLayout: DynamicLayoutConfig = {
      ...currentLayout,
      quickCreateConfig: {
        ...currentLayout.quickCreateConfig,
        section: { ...section }
      }
    };

    this.setQuickCreateSection(section);
    this.updateCurrentLayout(updatedLayout);
  }

  /**
   * ✅ BUG FIX 3: Sync Quick Create fields with updated Create tab fields
   * Updates Quick Create field names when they change in Create tab
   */
  private syncQuickCreateFieldsWithCreateTab(): void {
    const currentQuickCreateSection = this.quickCreateSection();
    if (!currentQuickCreateSection?.fields?.length) {
      // console.log('🔍 BUG FIX 3: No Quick Create fields to sync');
      return;
    }

    let hasChanges = false;
    const updatedFields = currentQuickCreateSection.fields.map((quickCreateField: any) => {
      // Find matching field in Create tab sections by ID and type
      const matchingField = this.findMatchingFieldInCreateTab(quickCreateField);

      if (matchingField && matchingField.label !== quickCreateField.label) {
        // console.log(`🔄 BUG FIX 3: Syncing field name: "${quickCreateField.label}" -> "${matchingField.label}"`);
        hasChanges = true;
        return {
          ...quickCreateField,
          label: matchingField.label
        };
      }

      return quickCreateField;
    });


    if (hasChanges) {
      // Update Quick Create section with synced field names
      const updatedSection = {
        ...currentQuickCreateSection,
        fields: updatedFields
      };

      // ✅ REFACTORED: Update state thông qua service
      this.setQuickCreateSection(updatedSection);
    }
  }

  /**
   * ✅ BUG FIX 3: Find matching field in Create tab sections
   */
  private findMatchingFieldInCreateTab(quickCreateField: Field): Field | null {
    const sections = this._createTabSections();
    for (const section of sections) {
      for (const field of section.fields || []) {
        // Match by ID and type for accuracy
        if ((field._id === quickCreateField._id || field._id === quickCreateField._id) &&
            field.type === quickCreateField.type) {
          return field;
        }
      }
    }
    return null;
  }
  
  /**
   * Cập nhật data từ Detail View Tab (detail view config)
   * @param config - DetailViewConfig mới
   */
  public updateDetailViewTabData(config: DetailViewConfig): void {
    const currentLayout = this.getCurrentLayout();
    if (!currentLayout) return;
    
    // console.log('📝 DynamicLayoutConfigStateService: Cập nhật Detail View Tab data');
    
    const updatedLayout: DynamicLayoutConfig = {
      ...currentLayout,
      detailViewConfig: { ...config }
    };
    
    this.setDetailViewConfig(updatedLayout.detailViewConfig);
    this.setDetailViewTabSections(updatedLayout.detailViewConfig?.sections || []);
    this.updateCurrentLayout(updatedLayout);
  }
  
  // ==================== LAYOUT CRUD METHODS ====================

  /**
   * Thêm layout mới vào config
   * @param layout - DynamicLayoutConfig mới
   * @returns boolean - true nếu thành công
   */
  public addLayout(layout: DynamicLayoutConfig): boolean {
    const currentState = this.stateSubject.value;
    if (!currentState) {
      // console.warn('⚠️ DynamicLayoutConfigStateService: State chưa được khởi tạo');
      return false;
    }

    // Kiểm tra trùng lặp ID
    const existingLayout = currentState.config.layouts.find(l => l._id === layout._id);
    if (existingLayout) {
      // console.warn('⚠️ DynamicLayoutConfigStateService: Layout ID đã tồn tại:', layout._id);
      return false;
    }

    // console.log('➕ DynamicLayoutConfigStateService: Thêm layout mới:', layout.title);

    const updatedConfig: DynamicLayoutBuilderConfig = {
      ...currentState.config,
      layouts: [...currentState.config.layouts, layout]
    };

    const updatedState: DynamicLayoutConfigState = {
      ...currentState,
      config: updatedConfig,
      lastModified: new Date()
    };

    this.stateSubject.next(updatedState);
    return true;
  }

  /**
   * Xóa layout khỏi config
   * @param layoutId - ID của layout cần xóa
   * @returns boolean - true nếu thành công
   */
  public removeLayout(layoutId: string): boolean {
    const currentState = this.stateSubject.value;
    if (!currentState) {
      // console.warn('⚠️ DynamicLayoutConfigStateService: State chưa được khởi tạo');
      return false;
    }

    // Không cho phép xóa layout cuối cùng
    if (currentState.config.layouts.length <= 1) {
      // console.warn('⚠️ DynamicLayoutConfigStateService: Không thể xóa layout cuối cùng');
      return false;
    }

    // Tìm layout cần xóa
    const layoutToRemove = currentState.config.layouts.find(l => l._id === layoutId);
    if (!layoutToRemove) {
      // console.warn('⚠️ DynamicLayoutConfigStateService: Không tìm thấy layout:', layoutId);
      return false;
    }

    // console.log('🗑️ DynamicLayoutConfigStateService: Xóa layout:', layoutToRemove.title);

    const updatedLayouts = currentState.config.layouts.filter(l => l._id !== layoutId);
    const updatedConfig: DynamicLayoutBuilderConfig = {
      ...currentState.config,
      layouts: updatedLayouts
    };

    // Nếu đang xóa layout hiện tại, chuyển sang layout đầu tiên
    let newCurrentLayout = currentState.currentLayout;
    if (currentState.currentLayout._id === layoutId) {
      newCurrentLayout = updatedLayouts[0];
      // console.log('🔄 DynamicLayoutConfigStateService: Chuyển sang layout:', newCurrentLayout.title);
    }

    const updatedState: DynamicLayoutConfigState = {
      ...currentState,
      config: updatedConfig,
      currentLayout: newCurrentLayout,
      lastModified: new Date()
    };

    this.stateSubject.next(updatedState);
    this.changeLayout(newCurrentLayout);

    return true;
  }

  changeLayout(newCurrentLayout: DynamicLayoutConfig) {
    // ✅ NEW: Khởi tạo Quick Create signals
    this._quickCreateFieldSections.set(newCurrentLayout.sections || []);
    this._quickCreateSection.set(newCurrentLayout.quickCreateConfig?.section || null);

    // ✅ NEW: Khởi tạo Create Tab signals
    this._createTabSections.set(newCurrentLayout.sections || []);
    this._createTabAvailableFieldTypes.set(getFilteredFieldTypes(newCurrentLayout.supportedFieldTypes));

    // ✅ NEW: Khởi tạo Detail View Tab signals
    this._detailViewTabSections.set(newCurrentLayout.detailViewConfig?.sections || []);
    this._detailViewTabAvailableWidgetTypes.set([]); // TODO: Implement widget types
    this._detailViewConfig.set(newCurrentLayout.detailViewConfig || null);

    this.currentLayoutSubject.next(newCurrentLayout);
  }



  // ==================== CLEANUP METHODS ====================

  /**
   * Cleanup service khi component bị destroy
   */
  public destroy(): void {
    // console.log('🧹 DynamicLayoutConfigStateService: Cleanup service');

    this.stateSubject.complete();
    this.currentLayoutSubject.complete();
    this.availableFieldTypesSubject.complete();
    this.unsavedChangesSubject.complete();
  }
}
