// Field Permission Modal Styles

.field-permission-content {
  min-width: 600px;
  max-width: 90vw;
  padding: 1rem;

  // Responsive table container
  .table-responsive {
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    
    // Table styling
    .table {
      margin-bottom: 0;
      
      // Header styling
      thead {
        th {
          border-bottom: 2px solid #dee2e6;
          font-weight: 600;
          vertical-align: middle;
          padding: 1rem 0.75rem;
          
          // Profile column
          &.profile-column {
            min-width: 200px;
            width: 40%;
          }
          
          // Permission columns
          &.permission-column {
            width: 20%;
            min-width: 120px;
            
            .btn {
              font-size: 0.75rem;
              padding: 0.25rem 0.75rem;
              border-radius: 0.25rem;
              transition: all 0.15s ease-in-out;
              
              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.15);
              }
            }
          }
        }
      }
      
      // Body styling
      tbody {
        tr {
          transition: background-color 0.15s ease-in-out;
          
          &:hover {
            background-color: rgba(0, 123, 255, 0.05);
          }
          
          td {
            vertical-align: middle;
            padding: 0.75rem;
            border-bottom: 1px solid #dee2e6;
            
            // Profile name styling
            &.profile-name {
              font-weight: 500;
              color: #495057;
            }
            
            // Radio button styling
            .form-check {
              margin: 0;
              
              .form-check-input {
                width: 1.25rem;
                height: 1.25rem;
                cursor: pointer;
                transition: all 0.15s ease-in-out;
                
                &:checked {
                  background-color: #0d6efd;
                  border-color: #0d6efd;
                  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
                }
                
                &:focus {
                  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
                }
                
                &:hover {
                  border-color: #0d6efd;
                }
              }
            }
          }
        }
      }
    }
  }
  
  // Alert styling
  .alert {
    border-radius: 0.375rem;
    border: none;
    
    &.alert-info {
      background-color: #cff4fc;
      color: #055160;
      
      .fas {
        color: #0dcaf0;
      }
    }
  }
}

// Dialog actions styling
mat-dialog-actions {
  border-top: 1px solid #dee2e6;
  background-color: #f8f9fa;
  
  .mat-mdc-button {
    margin-left: 0.5rem;
  }
  
  .mat-mdc-raised-button {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    
    &:hover {
      box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .field-permission-content {
    min-width: unset;
    padding: 0.5rem;
    
    .table-responsive {
      .table {
        font-size: 0.875rem;
        
        thead th {
          padding: 0.75rem 0.5rem;
          
          &.permission-column {
            min-width: 100px;
            
            .btn {
              font-size: 0.625rem;
              padding: 0.125rem 0.5rem;
            }
          }
        }
        
        tbody td {
          padding: 0.5rem;
          
          .form-check .form-check-input {
            width: 1rem;
            height: 1rem;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .field-permission-content {
    .table-responsive {
      .table {
        thead th {
          &.profile-column {
            min-width: 150px;
          }
          
          &.permission-column {
            min-width: 80px;
            
            span {
              font-size: 0.75rem;
            }
            
            .btn {
              font-size: 0.5rem;
              padding: 0.125rem 0.25rem;
            }
          }
        }
      }
    }
  }
}

// Animation cho buttons
@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

.btn:active {
  animation: buttonPress 0.1s ease-in-out;
}

// Focus styles for accessibility
.form-check-input:focus-visible {
  outline: 2px solid #0d6efd;
  outline-offset: 2px;
}

.btn:focus-visible {
  outline: 2px solid #0d6efd;
  outline-offset: 2px;
}
